import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { Observable } from 'rxjs';

import {
    CalendarEventDto,
    CreateCalendarEventForRestaurantBodyDto,
    CreateCalendarEventForRestaurantParamsDto,
    RemoveCalendarEventFromRestaurantParamsDto,
    SearchCalendarEventsBodyDto,
} from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

@Injectable({
    providedIn: 'root',
})
export class CalendarEventsService {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/calendar-events`;

    constructor(private readonly _http: HttpClient) {}

    searchCalendarEvents(body: SearchCalendarEventsBodyDto): Observable<ApiResultV2<CalendarEventDto[]>> {
        return this._http.post<ApiResultV2<CalendarEventDto[]>>(`${this.API_BASE_URL}/search`, body);
    }

    createCalendarEventForRestaurant(
        params: CreateCalendarEventForRestaurantParamsDto,
        body: CreateCalendarEventForRestaurantBodyDto
    ): Observable<ApiResultV2<CalendarEventDto>> {
        return this._http.post<ApiResultV2<CalendarEventDto>>(`${this.API_BASE_URL}/restaurants/${params.restaurantId}`, body);
    }

    removeCalendarEventFromRestaurant(params: RemoveCalendarEventFromRestaurantParamsDto): Observable<void> {
        return this._http.delete<void>(`${this.API_BASE_URL}/${params.calendarEventId}/restaurants/${params.restaurantId}`);
    }
}

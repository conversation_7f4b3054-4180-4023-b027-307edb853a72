import { DateTime, Settings } from 'luxon';

import { CalendarEventDto } from '@malou-io/package-dto';
import { EntityConstructor, formatEnglishDateWithSuffix, Locale } from '@malou-io/package-utils';

import { LocalStorage } from ':core/storage/local-storage';

import { Notification } from './notification.model';

export interface PostSuggestionNotificationData {
    restaurantIds: string[];
    event: CalendarEventDto;
}

type PostSuggestionNotificationProps = EntityConstructor<PostSuggestionNotification>;

export class PostSuggestionNotification extends Notification {
    data: PostSuggestionNotificationData;

    constructor(props: PostSuggestionNotificationProps) {
        super(props);
        this.data = props.data;
        this.data.event = props.data.event;
    }

    copyWith(props: Partial<PostSuggestionNotificationProps>): PostSuggestionNotification {
        return new PostSuggestionNotification({ ...this, ...props });
    }

    getFormattedEventDate = (): string => {
        const defaultLocale = Settings.defaultLocale;
        if (defaultLocale === Locale.EN) {
            return formatEnglishDateWithSuffix(DateTime.fromObject(this.data.event.date).toJSDate());
        }
        return DateTime.fromObject(this.data.event.date).toFormat('dd MMMM');
    };

    getNotificationName = (): string => (this.data.event.emoji ?? '') + (this.data.event.name[LocalStorage.getLang()] ?? '');

    getNotificationDescription(): string {
        return this.data.event.name[LocalStorage.getLang()] ?? '';
    }

    getNotificationEmoji = (): string => this.data.event.emoji ?? '🎉';
}

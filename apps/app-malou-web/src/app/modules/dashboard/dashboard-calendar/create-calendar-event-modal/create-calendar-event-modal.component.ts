import { NgClass } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, FormsModule, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { EmojiEvent } from '@ctrl/ngx-emoji-mart/ngx-emoji';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';

import { CalendarEventNameDto } from '@malou-io/package-dto';
import { ApplicationLanguage, mapLanguageStringToApplicationLanguage } from '@malou-io/package-utils';

import { eventTitleTextLimit } from ':core/constants';
import { ScreenSize, ScreenSizeService } from ':core/services/screen-size.service';
import { LocalStorage } from ':core/storage/local-storage';
import { EmojiPickerComponent } from ':shared/components/emoji-picker/emoji-picker.component';
import { InputDatePickerComponent } from ':shared/components/input-date-picker/input-date-picker.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { AutoUnsubscribeOnDestroy } from ':shared/decorators/auto-unsubscribe-on-destroy.decorator';
import { KillSubscriptions } from ':shared/interfaces';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { FlagPathResolverPipe } from ':shared/pipes/flag-path-resolver.pipe';

export interface CreateCalendarEventModalComponentData {
    date: Date;
}

export interface CreateCalendarEventModalComponentReturn {
    date: Date;
    name: CalendarEventNameDto;
    emoji: string;
}

@Component({
    selector: 'app-create-calendar-event-modal',
    templateUrl: './create-calendar-event-modal.component.html',
    imports: [
        MatButtonModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        EmojiPickerComponent,
        InputTextComponent,
        NgClass,
        TranslateModule,
        InputDatePickerComponent,
    ],
})
@AutoUnsubscribeOnDestroy()
export class CreateCalendarEventModalComponent implements OnInit, KillSubscriptions {
    readonly SvgIcon = SvgIcon;
    readonly killSubscriptions$: Subject<void> = new Subject<void>();

    flagImg = {};
    currentLang = LocalStorage.getLang();
    otherLangs: string[];
    translateOtherLanguages = false;
    applicationLanguages: string[] = Object.values(ApplicationLanguage);
    calendarEventForm: FormGroup<{
        emoji: FormControl<string>;
        name: FormGroup<{
            fr: FormControl<string | null>;
            en: FormControl<string | null>;
            it: FormControl<string | null>;
            es: FormControl<string | null>;
        }>;
        date: FormControl<Date>;
    }>;
    isSmallScreen = false;
    EVENT_TITLE_TEXT_LIMIT = eventTitleTextLimit;

    langText = {
        fr: this._translate.instant('events.language.french'),
        en: this._translate.instant('events.language.english'),
        es: this._translate.instant('events.language.spanish'),
        it: this._translate.instant('events.language.italian'),
    };

    constructor(
        private readonly _dialogRef: MatDialogRef<CreateCalendarEventModalComponent, CreateCalendarEventModalComponentReturn>,
        private readonly _translate: TranslateService,
        private readonly _fb: NonNullableFormBuilder,
        private readonly _screenSizeService: ScreenSizeService,
        @Inject(MAT_DIALOG_DATA)
        public readonly data: CreateCalendarEventModalComponentData
    ) {
        this._initCalendarEventForm();
    }

    ngOnInit(): void {
        this._screenSizeService.resize$.subscribe((elt) => {
            this.isSmallScreen = elt.size === ScreenSize.IsSmallScreen;
        });

        this._translate.onLangChange.pipe(takeUntil(this.killSubscriptions$)).subscribe((res) => {
            this.currentLang = mapLanguageStringToApplicationLanguage(res.lang);
        });

        const flagPathResolverPipe = new FlagPathResolverPipe();
        this.applicationLanguages.forEach((l) => {
            this.flagImg[l] = flagPathResolverPipe.transform(l);
        });
        this.otherLangs = this.applicationLanguages.filter((l) => l !== this.currentLang);
    }

    changeEmoji(event: EmojiEvent): void {
        const emoji = event.emoji.native;
        if (emoji) {
            this.calendarEventForm.controls.emoji.patchValue(emoji);
        }
    }

    save(): void {
        return this.close(this.calendarEventForm.getRawValue());
    }

    close(data?: CreateCalendarEventModalComponentReturn): void {
        this._dialogRef.close(data);
    }

    expandModalHeight(): void {
        if (this.isSmallScreen) {
            return;
        }
        const modal = document.querySelector('.malou-dialog-panel') as HTMLElement;
        modal.style.height = '505px';
    }

    getLangText(lang: string): string {
        return this.langText[lang];
    }

    getFlagImg(lang: string): string {
        return this.flagImg[lang];
    }

    getOtherLangsText(): string {
        return this.otherLangs.map((lang) => this.getLangText(lang)).join(', ');
    }

    private _initCalendarEventForm(): void {
        this.calendarEventForm = this._fb.group({
            emoji: new FormControl('🎉', { validators: Validators.required, nonNullable: true }),
            name: this._fb.group({
                fr: new FormControl<string | null>(null),
                en: new FormControl<string | null>(null),
                it: new FormControl<string | null>(null),
                es: new FormControl<string | null>(null),
            }),
            date: new FormControl(this.data.date, { validators: Validators.required, nonNullable: true }),
        });
    }
}

import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { DateTime } from 'luxon';

import { CalendarEventDto } from '@malou-io/package-dto';

import { LocalStorage } from ':core/storage/local-storage';

@Component({
    selector: 'app-special-hours-calendar-event',
    templateUrl: './special-hours-calendar-event.component.html',
    styleUrls: ['./special-hours-calendar-event.component.scss'],
    imports: [MatButtonModule, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SpecialHoursCalendarEventComponent {
    readonly calendarEvent = input.required<CalendarEventDto>();
    readonly disabled = input<boolean>(false);

    readonly validateHours = output<void>();

    readonly calendarEventName = computed(
        () =>
            (this.calendarEvent().emoji ? `${this.calendarEvent().emoji} ` : '') + (this.calendarEvent().name[LocalStorage.getLang()] ?? '')
    );
    readonly calendarEventDate = computed(() => DateTime.fromObject(this.calendarEvent().date).toLocaleString(DateTime.DATE_HUGE));

    validateHoursForCalendarEvent(): void {
        this.validateHours.emit();
    }
}

import { inject, Injectable } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { Store } from '@ngrx/store';
import { omit } from 'lodash';
import { forkJoin, Observable, switchMap } from 'rxjs';
import { map, take } from 'rxjs/operators';

import { ExperimentationService } from ':core/services/experimentation.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ReviewsService } from ':modules/reviews/reviews.service';
import { selectReviewsFilters } from ':modules/reviews/store/reviews.selectors';
import { Review, ReviewsFilters } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';
import { AbstractCsvService, CsvAsStringArrays, DEFAULT_CSV_PAGINATION } from ':shared/services/csv-services/csv-service.abstract';

import { MAX_DOWNLOAD_REVIEWS_DURATION_IN_DAYS } from './csv-e-reputation.interface';
import { createDateIntervalWithInDaysDurationCondition, getReviewCsvRowData } from './helper-functions';

interface Data {
    reviews: Review[];
}

@Injectable({ providedIn: 'root' })
export class ReviewsCsvService extends AbstractCsvService<Data> {
    private readonly _store = inject(Store);
    private readonly _reviewsService = inject(ReviewsService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);
    private readonly _shortNumberPipe = inject(ShortNumberPipe);

    readonly isNewSemanticAnalysisFeatureEnabled = toSignal(
        this._experimentationService.isFeatureEnabledForRestaurant$('release-new-semantic-analysis'),
        { initialValue: false }
    );

    constructor() {
        super();
    }

    protected override _getData$(): Observable<Data> {
        const reviewsFilters$: Observable<ReviewsFilters> = this._store.select(selectReviewsFilters);
        const restaurantId = this._restaurantsService.currentRestaurant._id;
        return forkJoin([reviewsFilters$.pipe(take(1))]).pipe(
            switchMap(([reviewsFilters]) => {
                const { startDate, endDate } = createDateIntervalWithInDaysDurationCondition(
                    reviewsFilters?.startDate,
                    reviewsFilters?.endDate,
                    MAX_DOWNLOAD_REVIEWS_DURATION_IN_DAYS
                );
                const reviews$ = this._reviewsService
                    .getSelectedRestaurantsReviewsPaginated([restaurantId], DEFAULT_CSV_PAGINATION, {
                        ...omit(reviewsFilters, ['restaurants', 'period', 'aggregatedViewRestaurants']),
                        startDate,
                        endDate,
                    })
                    .pipe(map((reviews) => reviews.reviews as Review[]));

                return forkJoin({ reviews: reviews$ });
            })
        );
    }

    protected override _getCsvDataRows({ reviews }: Data): CsvAsStringArrays {
        return reviews.map((review) => {
            const rating = this._shortNumberPipe.transform(review.rating);
            const platform = this._enumTranslatePipe.transform(review.key, 'platform_key');
            return getReviewCsvRowData(review, rating, platform, this.isNewSemanticAnalysisFeatureEnabled());
        });
    }

    protected override _getCsvHeaderRow(): CsvAsStringArrays[0] {
        return [
            'Review Date ',
            'Review Text',
            'Reviewer',
            'Rating',
            'Platform',
            'Answer 1',
            'Answer Date  1',
            'Answer 2',
            'Answer Date  2',
            'Answer 3',
            'Answer Date  3',
            'Atmosphere Positive',
            'Atmosphere Negative',
            'Service Positive',
            'Service Negative',
            'Food Positive',
            'Food Negative',
            'Price Positive',
            'Price Negative',
            'Hygiene Positive',
            'Hygiene Negative',
            'Wait Time Positive',
            'Wait Time Negative',
        ];
    }
}

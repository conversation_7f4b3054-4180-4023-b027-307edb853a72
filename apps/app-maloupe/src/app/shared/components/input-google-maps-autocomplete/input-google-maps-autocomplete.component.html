<div class="flex w-full flex-col">
    <div
        class="border-malou-border-primary flex h-12 self-stretch rounded-full border bg-white"
        appearance="outline"
        [class.malou-border-secondary]="!errorMessage && isFocused"
        [class.malou-border-error]="!!errorMessage">
        <input
            class="malou-text-12--medium box-border h-full w-full rounded-full border-0 px-5 outline-none"
            type="text"
            [class.italic]="isEmptyValue"
            [class.malou-text-12]="isEmptyValue"
            [placeholder]="placeholder()"
            [formControl]="control"
            [matAutocomplete]="auto"
            (focus)="isFocused = true"
            (blur)="isFocused = false"
            #addressInput />

        <mat-autocomplete (optionSelected)="locationSelectedFn($event)" #auto="matAutocomplete">
            @for (place of predictedPlaces(); track $index) {
                @let isEyeIconVisible = shouldDisplayEyeIcon | applyPure: place.types;
                <mat-option [value]="place.description">
                    <div class="flex items-center gap-2">
                        <div class="flex w-full flex-col py-1">
                            <span class="malou-text-13--semibold text-malou-text-title"> {{ place.structured_formatting.main_text }}</span>
                            <span class="malou-text-13--regular text-malou-text">
                                {{ place.structured_formatting.secondary_text }}
                            </span>
                        </div>

                        @if (isEyeIconVisible) {
                            <mat-icon
                                class="!mr-0 !h-4 !w-4 text-malou-text"
                                matTooltip="{{ 'maloupe.diagnostic_search.common.search_bar.eye_icon_tooltip' | translate }}"
                                [svgIcon]="SvgIcon.EYE_CLOSED"></mat-icon>
                        }
                    </div>
                </mat-option>
            }
        </mat-autocomplete>

        <div class="flex items-center pr-5">
            @if (control.disabled && searchRestaurantRateLimitPerMinuteExceeded()) {
                <mat-progress-spinner class="!h-4 !w-4" mode="indeterminate"></mat-progress-spinner>
            } @else {
                <mat-icon class="!h-4 !w-4 text-malou-primary" [svgIcon]="SvgIcon.LOCALISATION"></mat-icon>
            }
        </div>
    </div>
    @if (getPlaceDetailsRateLimitPerHourExceeded()) {
        <div class="malou-text-10 py-1 italic text-malou-state-error">
            {{ 'maloupe.diagnostic_search.common.search_bar.place_details_rate_limit_error' | translate }}
        </div>
    } @else if (getPlaceDiagnosticRateLimitPerDayExceeded()) {
        <div class="malou-text-10 py-1 italic text-malou-state-error">
            {{ 'maloupe.diagnostic_search.common.search_bar.place_diagnostic_rate_limit_error' | translate }}
        </div>
    }
    @if (errorMessage()) {
        <div class="malou-text-10 py-1 italic text-malou-state-error">{{ errorMessage() }}</div>
    }
</div>

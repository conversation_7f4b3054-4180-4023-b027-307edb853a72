import { Router } from 'express';
import { singleton } from 'tsyringe';

import AbstractRouter from ':helpers/abstracts/abstract-router';
import { authorize } from ':plugins/passport';

import CalendarEventsController from './calendar-events.controller';

@singleton()
export default class CalendarEventsRouter extends AbstractRouter {
    constructor(private _calendarEventsController: CalendarEventsController) {
        super();
    }

    init(): Router {
        this.router.post('/search', authorize(), (req, res, next) => this._calendarEventsController.searchCalendarEvents(req, res, next));

        this.router.post('/restaurants/:restaurantId', authorize(), (req, res, next) =>
            this._calendarEventsController.createCalendarEventForRestaurant(req, res, next)
        );

        this.router.delete('/:calendarEventId/restaurants/:restaurantId', authorize(), (req, res, next) =>
            this._calendarEventsController.removeCalendarEventFromRestaurant(req, res, next)
        );

        return this.router;
    }
}

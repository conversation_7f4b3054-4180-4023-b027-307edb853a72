import { Builder } from 'builder-pattern';

import { ICalendarEvent, newDbId } from '@malou-io/package-models';
import { CalendarEventCountry } from '@malou-io/package-utils';

const _buildCalendarEvent = (user: ICalendarEvent) => Builder<ICalendarEvent>(user);

export const getDefaultCalendarEvent = () =>
    _buildCalendarEvent({
        _id: newDbId(),
        name: {
            en: 'Name',
            fr: 'Nom',
            es: 'Nombre',
            it: 'Nome',
        },
        date: {
            day: 12,
            month: 1,
            year: 2025,
        },
        byDefault: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        country: CalendarEventCountry.FRANCE,
        emoji: '🎉',
        example: {
            en: 'Example',
            fr: 'Exemple',
        },
        ideas: {
            en: 'Ideas',
            fr: 'Idées',
        },
        isBankHoliday: false,
        shouldSuggestSpecialHourUpdate: false,
        shouldSuggestToPost: {
            active: false,
            concernedRestaurantCategories: [],
        },
    });

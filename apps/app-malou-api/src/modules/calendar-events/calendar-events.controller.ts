import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    CalendarEventDto,
    CreateCalendarEventForRestaurantBodyDto,
    createCalendarEventForRestaurantBodyValidator,
    CreateCalendarEventForRestaurantParamsDto,
    createCalendarEventForRestaurantParamsValidator,
    RemoveCalendarEventFromRestaurantParamsDto,
    removeCalendarEventFromRestaurantParamsValidator,
    SearchCalendarEventsBodyDto,
    searchCalendarEventsBodyValidator,
} from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';
import { RemoveCalendarEventFromRestaurantUseCase } from ':modules/calendar-events/use-cases/remove-calendar-event-from-restaurant/remove-calendar-event-from-restaurant.use-case';
import { SearchCalendarEventsUseCase } from ':modules/calendar-events/use-cases/search-calendar-events/search-calendar-events.use-case';

import { CreateCalendarEventForRestaurantUseCase } from './use-cases/create-calendar-event-for-restaurant/create-calendar-event-for-restaurant.use-case';

@singleton()
export default class CalendarEventsController {
    constructor(
        private readonly _createNewEventUseCase: CreateCalendarEventForRestaurantUseCase,
        private readonly _getEventsBetweenDates: SearchCalendarEventsUseCase,
        private readonly _removeCalendarEventFromRestaurant: RemoveCalendarEventFromRestaurantUseCase
    ) {}

    @Body(searchCalendarEventsBodyValidator)
    async searchCalendarEvents(
        req: Request<any, any, SearchCalendarEventsBodyDto>,
        res: Response<ApiResultV2<CalendarEventDto[]>>,
        next: NextFunction
    ) {
        try {
            const events = await this._getEventsBetweenDates.execute(req.body);
            return res.status(200).json({ data: events });
        } catch (err) {
            next(err);
        }
    }

    @Params(createCalendarEventForRestaurantParamsValidator)
    @Body(createCalendarEventForRestaurantBodyValidator)
    async createCalendarEventForRestaurant(
        req: Request<CreateCalendarEventForRestaurantParamsDto, any, CreateCalendarEventForRestaurantBodyDto>,
        res: Response<ApiResultV2<CalendarEventDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const event = req.body;
            const createdEventDto = await this._createNewEventUseCase.execute(restaurantId, event);

            return res.status(200).json({ data: createdEventDto });
        } catch (err) {
            next(err);
        }
    }

    @Params(removeCalendarEventFromRestaurantParamsValidator)
    async removeCalendarEventFromRestaurant(
        req: Request<RemoveCalendarEventFromRestaurantParamsDto>,
        res: Response<void>,
        next: NextFunction
    ) {
        try {
            const { calendarEventId, restaurantId } = req.params;
            await this._removeCalendarEventFromRestaurant.execute(calendarEventId, restaurantId);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    }
}

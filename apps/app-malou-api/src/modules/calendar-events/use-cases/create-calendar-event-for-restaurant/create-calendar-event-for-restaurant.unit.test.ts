import { container } from 'tsyringe';

import { ICalendarEvent, newDbId, toDbId } from '@malou-io/package-models';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

import { CreateCalendarEventForRestaurantUseCase } from './create-calendar-event-for-restaurant.use-case';

describe('CreateCalendarEventForRestaurantUseCase', () => {
    let createCalendarEventForRestaurantUseCase: CreateCalendarEventForRestaurantUseCase;

    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'CalendarEventsRepository']);
        createCalendarEventForRestaurantUseCase = container.resolve(CreateCalendarEventForRestaurantUseCase);
    });

    it('should create a calendar event and associate it with a restaurant', async () => {
        const TEST_RESTAURANT_ID = newDbId();
        const TEST_EVENT: Partial<ICalendarEvent> = {
            name: { en: 'Special Promotion' },
            date: { day: 2, month: 5, year: 2025 },
            byDefault: false,
            shouldSuggestSpecialHourUpdate: false,
            shouldSuggestToPost: { active: false, concernedRestaurantCategories: [] },
        };

        const testCase = new TestCaseBuilderV2<'restaurants' | 'calendarEvents'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant()._id(TEST_RESTAURANT_ID).calendarEvents([]).build()];
                    },
                },
                calendarEvents: {
                    data() {
                        return [];
                    },
                },
            },
            expectedResult() {
                return {
                    restaurantId: TEST_RESTAURANT_ID.toString(),
                    event: TEST_EVENT,
                };
            },
        });

        await testCase.build();
        const { restaurantId, event } = testCase.getExpectedResult();

        const createdEvent = await createCalendarEventForRestaurantUseCase.execute(restaurantId, event);

        expect(createdEvent).toMatchObject({
            name: event.name,
            date: event.date,
            byDefault: event.byDefault,
            shouldSuggestSpecialHourUpdate: event.shouldSuggestSpecialHourUpdate,
            shouldSuggestToPost: event.shouldSuggestToPost,
        });

        // Verify the event is associated with the restaurant
        const restaurantsRepository = container.resolve(RestaurantsRepository);
        const updatedRestaurant = await restaurantsRepository.findOne({
            filter: { _id: TEST_RESTAURANT_ID },
            projection: { calendarEvents: 1 },
            options: { lean: true },
        });

        expect(updatedRestaurant?.calendarEvents).toContainEqual(toDbId(createdEvent.id));
    });
});

import { singleton } from 'tsyringe';

import { CalendarEventDto, CreateCalendarEventForRestaurantBodyDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { CalendarEventCountry } from '@malou-io/package-utils';

import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class CreateCalendarEventForRestaurantUseCase {
    constructor(
        private readonly _calendarEventsRepository: CalendarEventsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute(restaurantId: string, body: CreateCalendarEventForRestaurantBodyDto): Promise<CalendarEventDto> {
        const newEvent = new CalendarEvent({
            id: newDbId().toString(),
            date: body.date,
            byDefault: false,
            name: body.name,
            country: CalendarEventCountry.ALL, // A event created by a restaurant is for all countries
            emoji: body.emoji,
            createdAt: new Date(),
            updatedAt: new Date(),
        });

        const createdEvent = await this._calendarEventsRepository.createEvent(newEvent);

        await this._restaurantsRepository.addCalendarEventToRestaurant(restaurantId, createdEvent.id);

        return createdEvent.toDto();
    }
}

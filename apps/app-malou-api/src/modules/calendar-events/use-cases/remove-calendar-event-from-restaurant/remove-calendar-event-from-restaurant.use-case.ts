import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';

import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class RemoveCalendarEventFromRestaurantUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _calendarEventsRepository: CalendarEventsRepository
    ) {}

    async execute(calendarEventId: string, restaurantId: string): Promise<void> {
        await this._restaurantsRepository.removeCalendarEvent(restaurantId, calendarEventId);

        const restaurantsCountUsingCalendarEvent = await this._restaurantsRepository.countRestaurantsUsingCalendarEvent(calendarEventId);
        // If it was the only restaurant using that event, also delete the event document
        if (restaurantsCountUsingCalendarEvent === 0) {
            await this._calendarEventsRepository.deleteOne({ filter: { _id: toDbId(calendarEventId) } });
        }
    }
}

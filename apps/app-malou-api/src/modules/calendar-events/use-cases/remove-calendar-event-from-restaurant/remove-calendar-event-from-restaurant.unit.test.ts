import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { DayMonthYear } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultCalendarEvent } from ':modules/calendar-events/tests/calendar-event.builder';
import { RemoveCalendarEventFromRestaurantUseCase } from ':modules/calendar-events/use-cases/remove-calendar-event-from-restaurant/remove-calendar-event-from-restaurant.use-case';
import restaurantsRepository from ':modules/restaurants/restaurants.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

describe('RemoveCalendarEventForRestaurantUseCase', () => {
    let removeCalendarEventForRestaurantUseCase: RemoveCalendarEventFromRestaurantUseCase;

    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'CalendarEventsRepository']);
        removeCalendarEventForRestaurantUseCase = container.resolve(RemoveCalendarEventFromRestaurantUseCase);
    });

    it('should remove a calendar event from a restaurant', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants' | 'calendarEvents'>({
            seeds: {
                restaurants: {
                    data() {
                        const restaurantId = newDbId();
                        return [getDefaultRestaurant()._id(restaurantId).calendarEvents([newDbId(), newDbId()]).build()];
                    },
                },
                calendarEvents: {
                    data(dependencies) {
                        const [event1Id, event2Id] = dependencies.restaurants()[0].calendarEvents;
                        return [
                            getDefaultCalendarEvent()
                                ._id(event1Id)
                                .date(DateTime.now().minus({ days: 5 }).toObject() as DayMonthYear) // todo remove 'as' when upgrading luxon
                                .build(),
                            getDefaultCalendarEvent()
                                ._id(event2Id)
                                .date(DateTime.now().plus({ days: 5 }).toObject() as DayMonthYear) // todo remove 'as' when upgrading luxon
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                const restaurant = dependencies.restaurants[0];
                const remainingEventId = restaurant.calendarEvents[1];
                return {
                    restaurantId: restaurant._id,
                    remainingEventId,
                };
            },
        });

        await testCase.build();
        const { restaurants } = testCase.getSeededObjects();
        const { _id: restaurantId } = restaurants[0];
        const { calendarEvents } = restaurants[0];
        const expectedResult = testCase.getExpectedResult();

        await removeCalendarEventForRestaurantUseCase.execute(calendarEvents[0].toString(), restaurantId.toString());

        const updatedRestaurant = await container
            .resolve(restaurantsRepository)
            .findOneOrFail({ filter: { _id: restaurantId }, projection: { calendarEvents: 1 } });

        expect(updatedRestaurant.calendarEvents).toEqual([expectedResult.remainingEventId]);
    });

    it('should do nothing if the calendar event does not exist for the restaurant', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        const restaurantId = newDbId();
                        return [getDefaultRestaurant()._id(restaurantId).calendarEvents([newDbId()]).build()];
                    },
                },
            },
            expectedResult(dependencies) {
                const restaurant = dependencies.restaurants[0];
                return {
                    restaurantId: restaurant._id,
                    calendarEvents: restaurant.calendarEvents,
                };
            },
        });

        await testCase.build();
        const { restaurants } = testCase.getSeededObjects();
        const { _id: restaurantId } = restaurants[0];
        const expectedResult = testCase.getExpectedResult();

        await removeCalendarEventForRestaurantUseCase.execute(newDbId().toString(), restaurantId.toString());

        const updatedRestaurant = await container
            .resolve(restaurantsRepository)
            .findOneOrFail({ filter: { _id: restaurantId }, projection: { calendarEvents: 1 } });

        expect(updatedRestaurant.calendarEvents).toEqual(expectedResult.calendarEvents);
    });
});

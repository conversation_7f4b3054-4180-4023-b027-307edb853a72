import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { CalendarEventCountry, DayMonthYear, RestaurantCalendarEventsCountry } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultCalendarEvent } from ':modules/calendar-events/tests/calendar-event.builder';
import { SearchCalendarEventsUseCase } from ':modules/calendar-events/use-cases/search-calendar-events/search-calendar-events.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

describe('GetEventsBetweenDatesUseCase', () => {
    let getEventsBetweenDatesUseCase: SearchCalendarEventsUseCase;

    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'CalendarEventsRepository']);
        getEventsBetweenDatesUseCase = container.resolve(SearchCalendarEventsUseCase);
    });

    it('should return filtered calendar events between the given dates', async () => {
        const testCase = new TestCaseBuilderV2<'calendarEvents' | 'restaurants'>({
            seeds: {
                calendarEvents: {
                    data() {
                        return [
                            getDefaultCalendarEvent()
                                .country(CalendarEventCountry.FRANCE)
                                .date(DateTime.now().minus({ days: 5 }).toObject() as DayMonthYear) // todo remove 'as' when upgrading luxon
                                .build(),
                            getDefaultCalendarEvent()
                                .country(CalendarEventCountry.UNITED_STATES)
                                .byDefault(true)
                                .date(DateTime.now().plus({ days: 5 }).toObject() as DayMonthYear) // todo remove 'as' when upgrading luxon
                                .build(),
                        ];
                    },
                },
                restaurants: {
                    data(dependencies) {
                        const restaurantId = newDbId();
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId)
                                .calendarEventsCountry(RestaurantCalendarEventsCountry.FRANCE)
                                .calendarEvents([dependencies.calendarEvents()[0]._id, dependencies.calendarEvents()[1]._id])
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCase.build();
        const { restaurants } = testCase.getSeededObjects();
        const { _id: restaurantId } = restaurants[0];

        const result = await getEventsBetweenDatesUseCase.execute({
            restaurantId: restaurantId.toString(),
            startDate: DateTime.now().minus({ days: 7 }).toObject() as DayMonthYear, // todo remove 'as' when upgrading luxon
            endDate: DateTime.now().plus({ days: 7 }).toObject() as DayMonthYear, // todo remove 'as' when upgrading luxon
            country: CalendarEventCountry.FRANCE,
        });

        expect(result.length).toBe(1);
    });

    it('should return an empty array if no events match the criteria', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        const restaurantId = newDbId();
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId)
                                .calendarEventsCountry(RestaurantCalendarEventsCountry.FRANCE)
                                .calendarEvents([
                                    getDefaultCalendarEvent()
                                        .country(CalendarEventCountry.FRANCE)
                                        .date(DateTime.now().minus({ days: 10 }).toObject() as DayMonthYear) // todo remove 'as' when upgrading luxon
                                        .build()._id,
                                ])
                                .build(),
                        ];
                    },
                },
            },
            expectedResult() {
                return [];
            },
        });

        await testCase.build();
        const { restaurants } = testCase.getSeededObjects();
        const { _id: restaurantId } = restaurants[0];
        const expectedResult = testCase.getExpectedResult();

        const result = await getEventsBetweenDatesUseCase.execute({
            restaurantId: restaurantId.toString(),
            startDate: DateTime.now().minus({ days: 7 }).toObject() as DayMonthYear, // todo remove 'as' when upgrading luxon
            endDate: DateTime.now().plus({ days: 7 }).toObject() as DayMonthYear, // todo remove 'as' when upgrading luxon
            country: CalendarEventCountry.FRANCE,
        });

        expect(result).toEqual(expectedResult);
    });
});

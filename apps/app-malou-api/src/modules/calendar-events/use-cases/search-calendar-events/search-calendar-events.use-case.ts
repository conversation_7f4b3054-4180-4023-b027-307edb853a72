import { singleton } from 'tsyringe';

import { CalendarEventDto, SearchCalendarEventsBodyDto } from '@malou-io/package-dto';

import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class SearchCalendarEventsUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _calendarEventsRepository: CalendarEventsRepository
    ) {}

    async execute(body: SearchCalendarEventsBodyDto): Promise<CalendarEventDto[]> {
        const { restaurantId, startDate, endDate, country } = body;

        const calendarEventIds = await this._restaurantsRepository.getCalendarEventIds(restaurantId);

        return await this._calendarEventsRepository.search({
            calendarEventIds,
            country,
            startDate,
            endDate,
        });
    }
}

import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { CalendarEventCountry, DayMonthYear, RestaurantCalendarEventsCountry } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';
import { getDefaultCalendarEvent } from ':modules/calendar-events/tests/calendar-event.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

import { GetHolidaysAndLocationEventsForRestaurantsUseCase } from './get-holidays-and-location-for-restaurants.use-case';

describe('GetHolidaysAndLocationEventsForRestaurantsUseCase', () => {
    let getHolidaysAndLocationEventsForRestaurantsUseCase: GetHolidaysAndLocationEventsForRestaurantsUseCase;

    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'CalendarEventsRepository']);
        getHolidaysAndLocationEventsForRestaurantsUseCase = container.resolve(GetHolidaysAndLocationEventsForRestaurantsUseCase);
    });

    it('should return holidays and location events for the given restaurants within the date range', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants' | 'calendarEvents'>({
            seeds: {
                restaurants: {
                    data() {
                        const restaurantId = newDbId();
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId)
                                .calendarEventsCountry(RestaurantCalendarEventsCountry.FRANCE)
                                .calendarEvents([newDbId(), newDbId()])
                                .build(),
                        ];
                    },
                },
                calendarEvents: {
                    data(dependencies) {
                        const [event1Id, event2Id] = dependencies.restaurants()[0].calendarEvents;
                        return [
                            getDefaultCalendarEvent()
                                ._id(event1Id)
                                .country(CalendarEventCountry.FRANCE)
                                .isBankHoliday(true)
                                .date(DateTime.now().minus({ days: 5 }).toObject() as DayMonthYear) // todo remove 'as' when upgrading luxon
                                .build(),
                            getDefaultCalendarEvent()
                                ._id(event2Id)
                                .country(CalendarEventCountry.ALL)
                                .byDefault(false)
                                .date(DateTime.now().plus({ days: 5 }).toObject() as DayMonthYear) // todo remove 'as' when upgrading luxon
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                const restaurant = dependencies.restaurants[0];
                const events = dependencies.calendarEvents;
                return events.map((event) => ({
                    calendarEvent: new CalendarEvent({ id: event._id.toString(), ...event }),
                    restaurantId: restaurant._id.toString(),
                }));
            },
        });

        await testCase.build();
        const { restaurants } = testCase.getSeededObjects();
        const { _id: restaurantId } = restaurants[0];
        const expectedResult = testCase.getExpectedResult();

        const result = await getHolidaysAndLocationEventsForRestaurantsUseCase.execute({
            restaurantIds: [restaurantId],
            startDate: DateTime.now().minus({ days: 10 }).toISO(),
            endDate: DateTime.now().plus({ days: 10 }).toISO(),
        });

        expect(result).toEqual(expectedResult);
    });

    it('should return an empty array if no events match the criteria', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants' | 'calendarEvents'>({
            seeds: {
                restaurants: {
                    data() {
                        const restaurantId = newDbId();
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId)
                                .calendarEventsCountry(RestaurantCalendarEventsCountry.FRANCE)
                                .calendarEvents([newDbId()])
                                .build(),
                        ];
                    },
                },
                calendarEvents: {
                    data(dependencies) {
                        const [eventId] = dependencies.restaurants()[0].calendarEvents;
                        return [
                            getDefaultCalendarEvent()
                                ._id(eventId)
                                .country(CalendarEventCountry.UNITED_STATES)
                                .isBankHoliday(false)
                                .date(DateTime.now().minus({ days: 20 }).toObject() as DayMonthYear) // todo remove 'as' when upgrading luxon
                                .build(),
                        ];
                    },
                },
            },
            expectedResult() {
                return [];
            },
        });

        await testCase.build();
        const { restaurants } = testCase.getSeededObjects();
        const { _id: restaurantId } = restaurants[0];
        const expectedResult = testCase.getExpectedResult();

        const result = await getHolidaysAndLocationEventsForRestaurantsUseCase.execute({
            restaurantIds: [restaurantId],
            startDate: DateTime.now().minus({ days: 10 }).toISO(),
            endDate: DateTime.now().plus({ days: 10 }).toISO(),
        });

        expect(result).toEqual(expectedResult);
    });
});

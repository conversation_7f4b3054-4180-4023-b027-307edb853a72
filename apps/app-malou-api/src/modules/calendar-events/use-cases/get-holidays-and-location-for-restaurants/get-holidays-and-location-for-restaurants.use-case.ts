import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { ID, toDbId } from '@malou-io/package-models';
import { CalendarEventCountry, DayMonthYear } from '@malou-io/package-utils';

import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class GetHolidaysAndLocationEventsForRestaurantsUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _calendarEventsRepository: CalendarEventsRepository
    ) {}

    async execute({
        restaurantIds,
        startDate,
        endDate,
    }: {
        restaurantIds: ID[];
        startDate: string;
        endDate: string;
    }): Promise<{ calendarEvent: CalendarEvent; restaurantId: string }[]> {
        const restaurants = await this._restaurantsRepository.find({
            filter: { _id: { $in: restaurantIds.map((restaurantId) => toDbId(restaurantId)) } },
            projection: { calendarEventsCountry: 1, address: 1, calendarEvents: 1 },
            options: { lean: true },
        });

        const startDMY = DateTime.fromJSDate(new Date(startDate)).toObject() as DayMonthYear; // todo remove 'as' when upgrading luxon
        const endDMY = DateTime.fromJSDate(new Date(endDate)).toObject() as DayMonthYear; // todo remove 'as' when upgrading luxon

        const calendarEvents = (
            await Promise.all(
                restaurants.map(async (restaurant) => {
                    const country = restaurant.calendarEventsCountry ?? CalendarEventCountry.FRANCE;

                    const events = await this._calendarEventsRepository.getBankHolidaysAndCustomEvents({
                        calendarEventIds: restaurant.calendarEvents.map((e) => e.toString()),
                        country,
                        startDate: startDMY,
                        endDate: endDMY,
                    });

                    return events.map((event) => ({
                        calendarEvent: event,
                        restaurantId: restaurant._id.toString(),
                    }));
                })
            )
        ).flat();

        return calendarEvents;
    }
}

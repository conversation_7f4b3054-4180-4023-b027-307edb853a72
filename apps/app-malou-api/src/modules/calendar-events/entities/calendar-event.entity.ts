import { Optional } from 'utility-types';

import { CalendarEventDto } from '@malou-io/package-dto';
import { CalendarEventCountryType, DayMonthYear, RemoveMethodsFromClass } from '@malou-io/package-utils';

const defaultShouldSuggestToPost = {
    active: false,
    concernedRestaurantCategories: [],
};
const defaultShouldSuggestSpecialHourUpdate = false;

export type CalendarEventProps = RemoveMethodsFromClass<CalendarEvent>;

interface CalendarEventNames {
    en?: string | null;
    fr?: string | null;
    es?: string | null;
    it?: string | null;
}

interface CalendarEventExample {
    en?: string;
    fr?: string;
}

export class CalendarEvent {
    id: string;
    date: DayMonthYear;
    emoji?: string;
    country: CalendarEventCountryType;
    name: CalendarEventNames;
    byDefault: boolean;
    example?: CalendarEventExample;
    ideas?: CalendarEventExample;
    isBankHoliday?: boolean;
    shouldSuggestSpecialHourUpdate: boolean;
    shouldSuggestToPost: {
        active: boolean;
        concernedRestaurantCategories: string[];
    };
    createdAt: Date;
    updatedAt: Date;

    constructor(calendarEvent: Optional<CalendarEventProps, 'shouldSuggestSpecialHourUpdate' | 'shouldSuggestToPost'>) {
        this.id = calendarEvent.id;
        this.date = calendarEvent.date;
        this.emoji = calendarEvent.emoji;
        this.country = calendarEvent.country;
        this.name = calendarEvent.name;
        this.byDefault = calendarEvent.byDefault;
        this.example = calendarEvent.example;
        this.ideas = calendarEvent.ideas;
        this.isBankHoliday = calendarEvent.isBankHoliday;
        this.shouldSuggestSpecialHourUpdate = calendarEvent.shouldSuggestSpecialHourUpdate ?? defaultShouldSuggestSpecialHourUpdate;
        this.shouldSuggestToPost = calendarEvent.shouldSuggestToPost ?? defaultShouldSuggestToPost;
        this.createdAt = calendarEvent.createdAt;
        this.updatedAt = calendarEvent.updatedAt;
    }

    toDto(): CalendarEventDto {
        return {
            id: this.id,
            date: this.date,
            emoji: this.emoji,
            country: this.country,
            name: this.name,
            byDefault: this.byDefault,
            example: this.example,
            ideas: this.ideas,
            isBankHoliday: this.isBankHoliday,
            shouldSuggestSpecialHourUpdate: this.shouldSuggestSpecialHourUpdate,
            shouldSuggestToPost: this.shouldSuggestToPost,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}

import { singleton } from 'tsyringe';

import { CalendarEventModel, EntityRepository, ICalendarEvent, toDbId, toDbIds } from '@malou-io/package-models';
import { CalendarEventCountry, CalendarEventCountryType, DayMonthYear } from '@malou-io/package-utils';

import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';

@singleton()
export default class CalendarEventsRepository extends EntityRepository<ICalendarEvent> {
    constructor() {
        super(CalendarEventModel);
    }

    async createEvent(event: CalendarEvent): Promise<CalendarEvent> {
        const doc = this._toDocument(event);
        const createdDocument = await this.create({ data: doc });
        return this._toEntity(createdDocument);
    }

    async search(filters: {
        calendarEventIds: string[];
        country: CalendarEventCountryType;
        startDate: DayMonthYear;
        endDate: DayMonthYear;
    }): Promise<CalendarEvent[]> {
        const res = await this.find({
            filter: {
                _id: { $in: toDbIds(filters.calendarEventIds) },
                country: { $in: [CalendarEventCountry.ALL, filters.country] },
                $expr: this._getBetweenDatesAggregationFilter(filters.startDate, filters.endDate),
            },
            options: { lean: true },
        });
        return res.map((e) => this._toEntity(e));
    }

    async getBankHolidaysAndCustomEvents(filters: {
        calendarEventIds: string[];
        country: CalendarEventCountryType;
        startDate: DayMonthYear;
        endDate: DayMonthYear;
    }): Promise<CalendarEvent[]> {
        const res = await this.find({
            filter: {
                _id: { $in: toDbIds(filters.calendarEventIds) },
                country: { $in: [CalendarEventCountry.ALL, filters.country] },
                $or: [{ byDefault: false }, { isBankHoliday: true }],
                $expr: this._getBetweenDatesAggregationFilter(filters.startDate, filters.endDate),
            },
            options: { lean: true },
        });
        return res.map((e) => this._toEntity(e));
    }

    async getEventsWithSpecialHoursSuggestion(filters: { startDate: DayMonthYear }, limit: number): Promise<CalendarEvent[]> {
        const res = await this.find({
            filter: {
                shouldSuggestSpecialHourUpdate: true,
                $expr: this._getBetweenDatesAggregationFilter(filters.startDate, { day: 0, month: 0, year: 0 }).$and[0],
            },
            options: { lean: true, limit },
        });
        return res.map((e) => this._toEntity(e));
    }

    async getEventsWithPostSuggestion(filters: { startDate: DayMonthYear; endDate: DayMonthYear }): Promise<CalendarEvent[]> {
        const res = await this.find({
            filter: {
                $or: [{ 'shouldSuggestToPost.active': true }, { byDefault: false }],
                $expr: this._getBetweenDatesAggregationFilter(filters.startDate, filters.endDate),
            },
            options: { lean: true },
        });
        return res.map((e) => this._toEntity(e));
    }

    async findEventsByDatesAndOnlyDefaultFilters(filters: {
        startDate: DayMonthYear;
        endDate: DayMonthYear;
        onlyDefaultEvents: boolean;
    }): Promise<CalendarEvent[]> {
        const res = await this.find({
            filter: {
                ...(filters.onlyDefaultEvents ? { byDefault: true } : {}),
                $expr: this._getBetweenDatesAggregationFilter(filters.startDate, filters.endDate),
            },
            options: { lean: true },
        });
        return res.map((e) => this._toEntity(e));
    }

    private _getBetweenDatesAggregationFilter(
        startDate: DayMonthYear,
        endDate: DayMonthYear
    ): { $and: [{ $gte: [object, object] }, { $lte: [object, object] }] } {
        const startDateMongoObject = {
            $dateFromParts: {
                day: startDate.day,
                month: startDate.month,
                year: startDate.year,
            },
        };
        const endDateMongoObject = {
            $dateFromParts: {
                day: endDate.day,
                month: endDate.month,
                year: endDate.year,
            },
        };
        const eventDateMongoObject = {
            $dateFromParts: {
                year: '$date.year',
                month: '$date.month',
                day: '$date.day',
            },
        };
        return { $and: [{ $gte: [eventDateMongoObject, startDateMongoObject] }, { $lte: [eventDateMongoObject, endDateMongoObject] }] };
    }

    private _toEntity(document: ICalendarEvent): CalendarEvent {
        return new CalendarEvent({
            id: document._id.toString(),
            date: document.date,
            emoji: document.emoji,
            country: document.country,
            name: {
                fr: document.name.fr,
                en: document.name.en,
                it: document.name.it,
                es: document.name.es,
            },
            byDefault: document.byDefault,
            example: document.example,
            ideas: document.ideas,
            isBankHoliday: document.isBankHoliday,
            shouldSuggestSpecialHourUpdate: document.shouldSuggestSpecialHourUpdate,
            shouldSuggestToPost: document.shouldSuggestToPost,
            createdAt: document.createdAt,
            updatedAt: document.updatedAt,
        });
    }

    private _toDocument(event: CalendarEvent): ICalendarEvent {
        return {
            _id: toDbId(event.id),
            date: event.date,
            emoji: event.emoji,
            country: event.country,
            name: event.name,
            byDefault: event.byDefault,
            createdAt: event.createdAt,
            updatedAt: event.updatedAt,
            example: event.example,
            ideas: event.ideas,
            isBankHoliday: event.isBankHoliday,
            shouldSuggestSpecialHourUpdate: event.shouldSuggestSpecialHourUpdate,
            shouldSuggestToPost: event.shouldSuggestToPost,
        };
    }
}

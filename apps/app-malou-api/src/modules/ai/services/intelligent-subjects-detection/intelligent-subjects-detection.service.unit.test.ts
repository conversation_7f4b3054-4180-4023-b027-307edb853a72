import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { IReview, newDbId } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    IntelligentSubjectName,
    PostedStatus,
    ReviewAnalysisSentiment,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import { ReviewsIntelligentSubjectsDetectionService } from ':microservices/reviews-intelligent-subjects-detection.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { DetectIntelligentSubjectsResponse } from ':modules/ai/interfaces/ai.interfaces';
import { IntelligentSubjectsDetectionService } from ':modules/ai/services/intelligent-subjects-detection/intelligent-subjects-detection.service';
import IntelligentSubjectAutomationsRepository from ':modules/automations/features/intelligent-subjects/repositories/intelligent-subjects.repository';
import { IntelligentSubjectsAutomationActionHandlerService } from ':modules/automations/features/intelligent-subjects/services/intelligent-subjects-automation-action-handler/intelligent-subjects-automation-action-handler.service';
import { getDefaultIntelligentSubjectAutomation } from ':modules/automations/tests/intelligent-subject-automation.builder';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { getDefaultUser } from ':modules/users/tests/user.builder';
import * as experimentationService from ':services/experimentations-service/experimentation.service';

const defaultAiResponse = [
    { intelligentSubject: IntelligentSubjectName.HYGIENE, isDetected: true, sentiment: ReviewAnalysisSentiment.NEGATIVE },
    { intelligentSubject: IntelligentSubjectName.DISCRIMINATION, isDetected: false, sentiment: undefined },
];

const defaultAiResponseWithPositiveSentiment = [
    { intelligentSubject: IntelligentSubjectName.HYGIENE, isDetected: true, sentiment: ReviewAnalysisSentiment.POSITIVE },
    { intelligentSubject: IntelligentSubjectName.DISCRIMINATION, isDetected: false, sentiment: undefined },
];

let intelligentSubjectsDetectionService: IntelligentSubjectsDetectionService;
let restaurantsRepository: RestaurantsRepository;
let aiInteractionsRepository: AiInteractionsRepository;
let reviewsIntelligentSubjectsDetectionService: ReviewsIntelligentSubjectsDetectionService;
let reviewsRepository: ReviewsRepository;
let intelligentSubjectsAutomationActionHandlerService: IntelligentSubjectsAutomationActionHandlerService;
let intelligentSubjectAutomationsRepository: IntelligentSubjectAutomationsRepository;
// Mock the isFeatureAvailableForRestaurant function
jest.mock(':services/experimentations-service/experimentation.service');

describe('IntelligentSubjectsDetectionService', () => {
    class ReviewsIntelligentSubjectsDetectionServiceMock {
        async detectIntelligentSubjects(_params: any): Promise<GenericAiServiceResponseType<DetectIntelligentSubjectsResponse>> {
            return Promise.resolve({ aiResponse: defaultAiResponse, aiInteractionDetails: [] });
        }
    }
    class IntelligentSubjectsAutomationActionHandlerServiceMock {
        async handleIntelligentSubjectsAutomationsAction(_params: any): Promise<void> {
            return Promise.resolve();
        }
    }

    beforeEach(() => {
        jest.clearAllMocks();
        container.clearInstances();

        registerRepositories([
            'RestaurantsRepository',
            'ReviewsRepository',
            'AiInteractionsRepository',
            'IntelligentSubjectAutomationsRepository',
            'UsersRepository',
        ]);
        container.register(ReviewsIntelligentSubjectsDetectionService, {
            useValue: new ReviewsIntelligentSubjectsDetectionServiceMock() as any,
        });

        container.register(IntelligentSubjectsAutomationActionHandlerService, {
            useValue: new IntelligentSubjectsAutomationActionHandlerServiceMock() as any,
        });

        intelligentSubjectsDetectionService = container.resolve(IntelligentSubjectsDetectionService);
        restaurantsRepository = container.resolve(RestaurantsRepository);
        aiInteractionsRepository = container.resolve(AiInteractionsRepository);
        reviewsIntelligentSubjectsDetectionService = container.resolve(ReviewsIntelligentSubjectsDetectionService);
        intelligentSubjectsAutomationActionHandlerService = container.resolve(IntelligentSubjectsAutomationActionHandlerService);
        reviewsRepository = container.resolve(ReviewsRepository);
        intelligentSubjectAutomationsRepository = container.resolve(IntelligentSubjectAutomationsRepository);
    });

    describe('detectIntelligentSubjectsForReviews', () => {
        it('should skip processing if release-reviews-intelligent-subjects feature flag is disabled', async () => {
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'reviews' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(newDbId()).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [getDefaultReview().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .active(true)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id;
            const review = seededObjects.reviews[0] as IReview;

            jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(false);
            restaurantsRepository.findOne = jest.fn();

            await intelligentSubjectsDetectionService.detectIntelligentSubjectsForReviews({
                restaurantId: restaurantId.toString(),
                reviews: [review],
            });

            expect(restaurantsRepository.findOne).not.toHaveBeenCalled();
        });

        it('should skip processing if no intelligent subjects automations are enabled for the restaurant', async () => {
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'reviews' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(newDbId()).build()];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .active(false)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [getDefaultReview().restaurantId(dependencies.restaurants()[0]._id).intelligentSubjects([]).build()];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id;
            const review = seededObjects.reviews[0] as IReview;

            jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(true);
            restaurantsRepository.findOne = jest.fn();

            await intelligentSubjectsDetectionService.detectIntelligentSubjectsForReviews({
                restaurantId: restaurantId.toString(),
                reviews: [review],
            });

            expect(restaurantsRepository.findOne).not.toHaveBeenCalled();
        });
        it('should skip processing if all reviews have intelligent subjects', async () => {
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'reviews' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(newDbId()).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .intelligentSubjects([
                                        {
                                            subject: IntelligentSubjectName.HYGIENE,
                                            isDetected: true,
                                            sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                        },
                                    ])
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .intelligentSubjects([
                                        {
                                            subject: IntelligentSubjectName.DISCRIMINATION,
                                            isDetected: true,
                                            sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .active(true)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id;
            const reviews = seededObjects.reviews as IReview[];

            jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(true);
            aiInteractionsRepository.createAiInteraction = jest.fn();

            await intelligentSubjectsDetectionService.detectIntelligentSubjectsForReviews({
                restaurantId: restaurantId.toString(),
                reviews,
            });

            expect(aiInteractionsRepository.createAiInteraction).not.toHaveBeenCalled();
        });

        it('should skip processing if all reviews have no text', async () => {
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'reviews' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(newDbId()).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .intelligentSubjects([])
                                    .text(null)
                                    .build(),
                                getDefaultReview().restaurantId(dependencies.restaurants()[0]._id).intelligentSubjects([]).text('').build(),
                            ];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .active(true)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id;
            const reviews = seededObjects.reviews as IReview[];

            jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(true);
            aiInteractionsRepository.createAiInteraction = jest.fn();

            await intelligentSubjectsDetectionService.detectIntelligentSubjectsForReviews({
                restaurantId: restaurantId.toString(),
                reviews,
            });

            expect(aiInteractionsRepository.createAiInteraction).not.toHaveBeenCalled();
        });

        it('should skip processing if all reviews have comments', async () => {
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'reviews' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(newDbId()).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .comments([{ text: 'comment text', _id: newDbId(), posted: PostedStatus.POSTED }])
                                    .build(),
                            ];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .active(true)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id;
            const reviews = seededObjects.reviews as IReview[];

            jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(true);
            aiInteractionsRepository.createAiInteraction = jest.fn();

            await intelligentSubjectsDetectionService.detectIntelligentSubjectsForReviews({
                restaurantId: restaurantId.toString(),
                reviews,
            });

            expect(aiInteractionsRepository.createAiInteraction).not.toHaveBeenCalled();
        });

        it('should skip processing if all reviews are older than the oldest active automation', async () => {
            const reviewAvailableDate = DateTime.now().minus({ days: 10 }).toJSDate();
            const oldestActiveAutomationDate = DateTime.now().minus({ days: 5 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'reviews' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(newDbId()).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .intelligentSubjects(undefined)
                                    .text('review text 1')
                                    .comments([])
                                    .socialCreatedAt(reviewAvailableDate)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .intelligentSubjects(undefined)
                                    .text('review text 2')
                                    .comments([])
                                    .socialCreatedAt(reviewAvailableDate)
                                    .build(),
                            ];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .lastActiveUpdatedAt(oldestActiveAutomationDate)
                                    .active(true)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id;
            const reviews = seededObjects.reviews as IReview[];

            jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(true);
            aiInteractionsRepository.createAiInteraction = jest.fn();

            await intelligentSubjectsDetectionService.detectIntelligentSubjectsForReviews({
                restaurantId: restaurantId.toString(),
                reviews,
            });

            expect(aiInteractionsRepository.createAiInteraction).not.toHaveBeenCalled();
        });

        it('should detect intelligent subjects for multiple reviews correctly and trigger automations', async () => {
            const reviewText1 = 'review text 1';
            const reviewText2 = 'review text 2';
            const socialId0 = 'social_id_0';
            const socialId1 = 'social_id_1';
            const socialId2 = 'social_id_2';
            const socialId3 = 'social_id_3';
            const socialId4 = 'social_id_4';
            const socialId5 = 'social_id_5';

            const reviewAvailableDate = DateTime.now().minus({ days: 10 }).toJSDate();
            const oldestActiveAutomationDate = DateTime.now().minus({ days: 5 }).toJSDate();

            const testCase = new TestCaseBuilderV2<
                'users' | 'restaurants' | 'reviews' | 'aiInteractions' | 'intelligentSubjectAutomations'
            >({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(newDbId()).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                // review too old (should be skipped)
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .intelligentSubjects(undefined)
                                    .text(reviewText1)
                                    .comments([])
                                    .socialId(socialId0)
                                    .socialCreatedAt(reviewAvailableDate)
                                    .build(),
                                // review with no text (should be skipped)
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .intelligentSubjects([])
                                    .text(null)
                                    .comments([])
                                    .socialId(socialId1)
                                    .build(),
                                // review with empty text (should be skipped)
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .intelligentSubjects([])
                                    .text('')
                                    .comments([])
                                    .socialId(socialId2)
                                    .build(),
                                // review with intelligent subjects (should be skipped)
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .intelligentSubjects([
                                        {
                                            subject: IntelligentSubjectName.HYGIENE,
                                            isDetected: true,
                                            sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                        },
                                    ])
                                    .comments([])
                                    .socialId(socialId3)
                                    .build(),

                                // review with no intelligent subjects (should be processed)
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .text(reviewText1)
                                    .intelligentSubjects([])
                                    .comments([])
                                    .socialId(socialId4)
                                    .build(),
                                // review with no intelligent subjects (should be processed)
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .text(reviewText2)
                                    .intelligentSubjects([])
                                    .comments([])
                                    .socialId(socialId5)
                                    .build(),
                            ];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .active(true)
                                    .lastActiveUpdatedAt(oldestActiveAutomationDate)
                                    .build(),
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .active(true)
                                    .lastActiveUpdatedAt(oldestActiveAutomationDate)
                                    .build(),
                            ];
                        },
                    },
                    aiInteractions: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id;
            const reviews = seededObjects.reviews;
            const user = seededObjects.users[0];
            const automations = seededObjects.intelligentSubjectAutomations.map((automation) =>
                intelligentSubjectAutomationsRepository.toEntity({
                    ...automation,
                    user: { _id: user._id, defaultLanguage: user.defaultLanguage },
                })
            );

            jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(true);
            jest.spyOn(reviewsIntelligentSubjectsDetectionService, 'detectIntelligentSubjects');
            jest.spyOn(aiInteractionsRepository, 'createAiInteraction');
            jest.spyOn(intelligentSubjectsAutomationActionHandlerService, 'handleIntelligentSubjectsAutomationsAction');

            await intelligentSubjectsDetectionService.detectIntelligentSubjectsForReviews({
                restaurantId: restaurantId.toString(),
                reviews,
            });

            expect(aiInteractionsRepository.createAiInteraction).toHaveBeenCalledTimes(2);

            expect(reviewsIntelligentSubjectsDetectionService.detectIntelligentSubjects).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: AiInteractionType.INTELLIGENT_SUBJECTS_DETECTION,
                    relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                    restaurantData: {
                        reviewText: reviewText1,
                        intelligentSubjects: [IntelligentSubjectName.DISCRIMINATION, IntelligentSubjectName.HYGIENE].map((isn) =>
                            isn.toLowerCase()
                        ),
                    },
                })
            );
            expect(reviewsIntelligentSubjectsDetectionService.detectIntelligentSubjects).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: AiInteractionType.INTELLIGENT_SUBJECTS_DETECTION,
                    relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                    restaurantData: {
                        reviewText: reviewText2,
                        intelligentSubjects: [IntelligentSubjectName.DISCRIMINATION, IntelligentSubjectName.HYGIENE].map((isn) =>
                            isn.toLowerCase()
                        ),
                    },
                })
            );

            const updatedReviews = await reviewsRepository.find({
                filter: { socialId: { $in: [socialId4, socialId5] } },
                options: { lean: true },
            });

            const mappedDefaultAiResponse = defaultAiResponse.map((subject) => ({
                subject: subject.intelligentSubject,
                isDetected: subject.isDetected,
                sentiment: subject.sentiment || undefined,
            }));

            expect(updatedReviews[0].intelligentSubjects).toIncludeSameMembers(mappedDefaultAiResponse);
            expect(updatedReviews[1].intelligentSubjects).toIncludeSameMembers(mappedDefaultAiResponse);

            expect(intelligentSubjectsAutomationActionHandlerService.handleIntelligentSubjectsAutomationsAction).toHaveBeenCalledWith(
                expect.objectContaining({
                    automations: automations.filter((automation) => automation.subject === IntelligentSubjectName.HYGIENE),
                    entityId: reviews[4]._id.toString(),
                })
            );

            expect(intelligentSubjectsAutomationActionHandlerService.handleIntelligentSubjectsAutomationsAction).toHaveBeenCalledWith(
                expect.objectContaining({
                    automations: automations.filter((automation) => automation.subject === IntelligentSubjectName.HYGIENE),
                    entityId: reviews[5]._id.toString(),
                })
            );
        });

        it('should detect intelligent subjects for reviews but skip automations if no matching subjects', async () => {
            const oldestActiveAutomationDate = DateTime.now().minus({ days: 5 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'reviews' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(newDbId()).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .text('review text')
                                    .intelligentSubjects([])
                                    .comments([])
                                    .build(),
                            ];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .active(false)
                                    .lastActiveUpdatedAt(oldestActiveAutomationDate)
                                    .build(),
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .active(true)
                                    .lastActiveUpdatedAt(oldestActiveAutomationDate)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id;
            const reviews = seededObjects.reviews;

            jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(true);
            jest.spyOn(reviewsIntelligentSubjectsDetectionService, 'detectIntelligentSubjects');
            jest.spyOn(aiInteractionsRepository, 'createAiInteraction');
            jest.spyOn(intelligentSubjectsAutomationActionHandlerService, 'handleIntelligentSubjectsAutomationsAction');

            await intelligentSubjectsDetectionService.detectIntelligentSubjectsForReviews({
                restaurantId: restaurantId.toString(),
                reviews,
            });

            expect(aiInteractionsRepository.createAiInteraction).toHaveBeenCalledTimes(1);
            expect(reviewsIntelligentSubjectsDetectionService.detectIntelligentSubjects).toHaveBeenCalledTimes(1);
            expect(intelligentSubjectsAutomationActionHandlerService.handleIntelligentSubjectsAutomationsAction).not.toHaveBeenCalled();
        });

        it('should detect intelligent subjects for reviews but skip automations if detected subject has a positive sentiment', async () => {
            reviewsIntelligentSubjectsDetectionService = container.resolve(ReviewsIntelligentSubjectsDetectionService);
            jest.spyOn(reviewsIntelligentSubjectsDetectionService, 'detectIntelligentSubjects').mockResolvedValueOnce({
                aiResponse: defaultAiResponseWithPositiveSentiment,
                aiInteractionDetails: [],
            });

            const oldestActiveAutomationDate = DateTime.now().minus({ days: 5 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'reviews' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(newDbId()).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .text('review text')
                                    .intelligentSubjects([])
                                    .comments([])
                                    .build(),
                            ];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .active(true)
                                    .lastActiveUpdatedAt(oldestActiveAutomationDate)
                                    .build(),
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .active(false)
                                    .lastActiveUpdatedAt(oldestActiveAutomationDate)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id;
            const reviews = seededObjects.reviews;

            jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(true);
            jest.spyOn(reviewsIntelligentSubjectsDetectionService, 'detectIntelligentSubjects');
            jest.spyOn(aiInteractionsRepository, 'createAiInteraction');
            jest.spyOn(intelligentSubjectsAutomationActionHandlerService, 'handleIntelligentSubjectsAutomationsAction');

            await intelligentSubjectsDetectionService.detectIntelligentSubjectsForReviews({
                restaurantId: restaurantId.toString(),
                reviews,
            });

            expect(aiInteractionsRepository.createAiInteraction).toHaveBeenCalledTimes(1);
            expect(reviewsIntelligentSubjectsDetectionService.detectIntelligentSubjects).toHaveBeenCalledTimes(1);
            expect(intelligentSubjectsAutomationActionHandlerService.handleIntelligentSubjectsAutomationsAction).not.toHaveBeenCalled();
        });
    });
});

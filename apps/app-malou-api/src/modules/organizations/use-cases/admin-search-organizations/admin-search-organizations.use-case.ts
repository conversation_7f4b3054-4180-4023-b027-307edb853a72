import { ReadPreference } from 'mongodb';
import { singleton } from 'tsyringe';

import {
    AdminSearchOrganizationsOrganizationDto,
    AdminSearchOrganizationsQueryDto,
    AdminSearchOrganizationsResponseDto,
} from '@malou-io/package-dto';

import { toDiacriticInsensitiveRegexString } from ':helpers/utils';
import OrganizationsRepository from ':modules/organizations/organizations.repository';

@singleton()
export class AdminSearchOrganizationsUseCase {
    constructor(private readonly _organizationsRepository: OrganizationsRepository) {}

    async execute(params: AdminSearchOrganizationsQueryDto): Promise<AdminSearchOrganizationsResponseDto> {
        const { text, limit = 20, offset = 0 } = params;

        const pipeline: any[] = [];

        const trimmedText = text?.trim();
        if (trimmedText) {
            const searchRegex = toDiacriticInsensitiveRegexString(trimmedText);
            pipeline.push({
                $match: {
                    name: { $regex: searchRegex, $options: 'i' },
                },
            });
        }

        pipeline.push({
            $sort: {
                createdAt: -1,
                name: 1,
            },
        });

        const countPipeline = [...pipeline];
        countPipeline.push({ $count: 'total' });

        pipeline.push({ $skip: offset });
        pipeline.push({ $limit: limit });

        pipeline.push({
            $lookup: {
                from: 'users',
                localField: '_id',
                foreignField: 'organizationIds',
                as: 'users',
                pipeline: [
                    {
                        $project: {
                            _id: 1,
                            name: 1,
                            lastname: 1,
                            email: 1,
                            organizationIds: 1,
                        },
                    },
                ],
            },
        });

        pipeline.push({
            $lookup: {
                from: 'restaurants',
                localField: '_id',
                foreignField: 'organizationId',
                as: 'restaurants',
                pipeline: [
                    {
                        $match: { active: true },
                    },
                    {
                        $project: {
                            _id: 1,
                            name: 1,
                            active: 1,
                        },
                    },
                ],
            },
        });

        const [organizations, countResult] = await Promise.all([
            this._organizationsRepository.aggregate(pipeline, {
                readPreference: ReadPreference.SECONDARY_PREFERRED,
                comment: 'adminSearchOrganizations',
            }),
            this._organizationsRepository.aggregate(countPipeline, {
                readPreference: ReadPreference.SECONDARY_PREFERRED,
                comment: 'adminSearchOrganizationsCount',
            }),
        ]);

        const total = countResult.length > 0 ? countResult[0].total : 0;

        return {
            data: organizations.map((organization) => this._toDto(organization)),
            total,
        };
    }

    private _toDto(organization: any): AdminSearchOrganizationsOrganizationDto {
        return {
            _id: organization._id.toString(),
            name: organization.name,
            limit: organization.limit,
            verifiedEmailsForCampaigns: organization.verifiedEmailsForCampaigns || [],
            createdAt: organization.createdAt,
            updatedAt: organization.updatedAt,
            users:
                organization.users?.map((user: any) => ({
                    _id: user._id.toString(),
                    name: user.name,
                    lastname: user.lastname,
                    email: user.email,
                    organizationIds: user.organizationIds?.map((id: any) => id.toString()) || [],
                })) || [],
            restaurants:
                organization.restaurants?.map((restaurant: any) => ({
                    _id: restaurant._id.toString(),
                    name: restaurant.name,
                    active: restaurant.active,
                })) || [],
        };
    }
}

import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import {
    DayMonthYear,
    NotificationChannel,
    NotificationType,
    PERIOD_FOR_EMAIL_POSTS_SUGGESTION,
    PERIOD_FOR_WEB_SPECIAL_HOUR,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';
import { getDefaultCalendarEvent } from ':modules/calendar-events/tests/calendar-event.builder';

import { NotificationEventsService } from './notification-events.service';

describe('NotificationEventsService', () => {
    beforeAll(() => {
        registerRepositories(['CalendarEventsRepository']);
    });

    it('should return 3 upcoming events for post suggestion (email)', async () => {
        const notificationEventsService = container.resolve(NotificationEventsService);

        const testCase = new TestCaseBuilderV2<'calendarEvents'>({
            seeds: {
                calendarEvents: {
                    data: () => {
                        return [
                            getDefaultCalendarEvent()
                                .isBankHoliday(true)
                                .shouldSuggestToPost({
                                    active: true,
                                    concernedRestaurantCategories: [],
                                })
                                .date(DateTime.now().plus({ days: PERIOD_FOR_EMAIL_POSTS_SUGGESTION }).toObject() as DayMonthYear)
                                .build(),
                            getDefaultCalendarEvent()
                                .byDefault(false)
                                .shouldSuggestToPost({
                                    active: true,
                                    concernedRestaurantCategories: [],
                                })
                                .date(DateTime.now().plus({ days: PERIOD_FOR_EMAIL_POSTS_SUGGESTION }).toObject() as DayMonthYear)
                                .build(),
                            getDefaultCalendarEvent()
                                .shouldSuggestToPost({
                                    active: true,
                                    concernedRestaurantCategories: [],
                                })
                                .date(
                                    DateTime.now()
                                        .plus({ days: PERIOD_FOR_EMAIL_POSTS_SUGGESTION - 1 })
                                        .toObject() as DayMonthYear
                                )
                                .build(),
                            getDefaultCalendarEvent()
                                .isBankHoliday(true)
                                .shouldSuggestToPost({
                                    active: true,
                                    concernedRestaurantCategories: [],
                                })
                                .date(DateTime.now().plus({ days: PERIOD_FOR_EMAIL_POSTS_SUGGESTION }).toObject() as DayMonthYear)
                                .build(),
                            getDefaultCalendarEvent()
                                .isBankHoliday(true)
                                .shouldSuggestToPost({
                                    active: true,
                                    concernedRestaurantCategories: [],
                                })
                                .date(
                                    DateTime.now()
                                        .plus({ days: PERIOD_FOR_EMAIL_POSTS_SUGGESTION + 2 })
                                        .toObject() as DayMonthYear
                                )
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): CalendarEvent[] => {
                const e0 = dependencies.calendarEvents[0];
                const e1 = dependencies.calendarEvents[1];
                const e3 = dependencies.calendarEvents[3];
                return [
                    new CalendarEvent({ id: e0._id.toString(), ...e0 }),
                    new CalendarEvent({ id: e1._id.toString(), ...e1 }),
                    new CalendarEvent({ id: e3._id.toString(), ...e3 }),
                ];
            },
        });

        await testCase.build();
        const expectedResult = testCase.getExpectedResult();
        const results = await notificationEventsService.getNotificationEvents({
            type: NotificationType.POST_SUGGESTION,
            channel: NotificationChannel.EMAIL,
            onlyDefaultEvents: false,
        });
        expect(results).toIncludeSameMembers(expectedResult);
    });

    it('should return 2 upcoming events for special hour (web)', async () => {
        const notificationEventsService = container.resolve(NotificationEventsService);

        const testCase = new TestCaseBuilderV2<'calendarEvents'>({
            seeds: {
                calendarEvents: {
                    data: () => {
                        return [
                            getDefaultCalendarEvent()
                                .isBankHoliday(true)
                                .shouldSuggestSpecialHourUpdate(true)
                                .date(DateTime.now().plus({ days: PERIOD_FOR_WEB_SPECIAL_HOUR }).toObject() as DayMonthYear) // todo remove 'as' when upgrading luxon
                                .build(),
                            getDefaultCalendarEvent()
                                .byDefault(false)
                                .shouldSuggestSpecialHourUpdate(false)
                                .date(DateTime.now().plus({ days: PERIOD_FOR_WEB_SPECIAL_HOUR }).toObject() as DayMonthYear) // todo remove 'as' when upgrading luxon
                                .build(),
                            getDefaultCalendarEvent()
                                .shouldSuggestSpecialHourUpdate(true)
                                .date(
                                    DateTime.now()
                                        .plus({ days: PERIOD_FOR_WEB_SPECIAL_HOUR - 1 })
                                        .toObject() as DayMonthYear
                                )
                                .build(),
                            getDefaultCalendarEvent()
                                .isBankHoliday(true)
                                .shouldSuggestSpecialHourUpdate(true)
                                .date(DateTime.now().plus({ days: PERIOD_FOR_WEB_SPECIAL_HOUR }).toObject() as DayMonthYear) // todo remove 'as' when upgrading luxon
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): CalendarEvent[] => {
                const e0 = dependencies.calendarEvents[0];
                const e1 = dependencies.calendarEvents[3];
                return [new CalendarEvent({ id: e0._id.toString(), ...e0 }), new CalendarEvent({ id: e1._id.toString(), ...e1 })];
            },
        });

        await testCase.build();
        const expectedResult = testCase.getExpectedResult();
        const results = await notificationEventsService.getNotificationEvents({
            type: NotificationType.SPECIAL_HOUR,
            channel: NotificationChannel.WEB,
            onlyDefaultEvents: true,
        });
        expect(results).toIncludeSameMembers(expectedResult);
    });
});

import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import {
    DayMonthYear,
    NotificationChannel,
    NotificationType,
    PERIOD_FOR_EMAIL_POSTS_SUGGESTION,
    PERIOD_FOR_EMAIL_SPECIAL_HOUR,
    PERIOD_FOR_WEB_POSTS_SUGGESTION,
    PERIOD_FOR_WEB_SPECIAL_HOUR,
} from '@malou-io/package-utils';

import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';

@singleton()
export class NotificationEventsService {
    private readonly _NOTIFICATION_CONFIG = {
        [NotificationType.POST_SUGGESTION]: {
            [NotificationChannel.EMAIL]: {
                period: PERIOD_FOR_EMAIL_POSTS_SUGGESTION,
                filterFunction: (event: CalendarEvent) => event.shouldSuggestToPost?.active || !event.byDefault,
            },
            [NotificationChannel.WEB]: {
                period: PERIOD_FOR_WEB_POSTS_SUGGESTION,
                filterFunction: (event: CalendarEvent) => event.shouldSuggestToPost?.active || !event.byDefault,
            },
        },
        [NotificationType.SPECIAL_HOUR]: {
            [NotificationChannel.EMAIL]: {
                period: PERIOD_FOR_EMAIL_SPECIAL_HOUR,
                filterFunction: (event: CalendarEvent) => event.shouldSuggestSpecialHourUpdate,
            },
            [NotificationChannel.WEB]: {
                period: PERIOD_FOR_WEB_SPECIAL_HOUR,
                filterFunction: (event: CalendarEvent) => event.shouldSuggestSpecialHourUpdate,
            },
        },
    };

    constructor(private readonly _calendarEventsRepository: CalendarEventsRepository) {}

    public async getNotificationEvents({
        type,
        channel,
        onlyDefaultEvents,
    }: {
        type: NotificationType.POST_SUGGESTION | NotificationType.SPECIAL_HOUR;
        channel: NotificationChannel.EMAIL | NotificationChannel.WEB;
        onlyDefaultEvents: boolean;
    }): Promise<CalendarEvent[]> {
        const config = this._NOTIFICATION_CONFIG[type][channel];

        const startDateTime = DateTime.now().plus({ days: config.period });
        const startDate = startDateTime.toObject() as DayMonthYear; // todo remove 'as' when upgrading luxon
        const endDate = startDateTime.plus({ days: 1 }).toObject() as DayMonthYear; // todo remove 'as' when upgrading luxon
        const upcomingCalendarEvents = await this._calendarEventsRepository.findEventsByDatesAndOnlyDefaultFilters({
            startDate,
            endDate,
            onlyDefaultEvents,
        });
        const upcomingEvents = upcomingCalendarEvents.filter(config.filterFunction);
        return upcomingEvents;
    }
}

import { DateTime } from 'luxon';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import {
    NotificationDTO,
    SummaryCommentProps,
    SummaryEmailNotificationProps,
    SummaryInformationUpdateErrorProps,
    SummaryMentionProps,
    SummaryMessageProps,
    SummaryPostErrorProps,
    SummaryPostSuggestionProps,
    SummaryReviewProps,
    SummarySpecialHourProps,
} from '@malou-io/package-dto';
import { SummaryNotificationTemplate } from '@malou-io/package-emails';
import { HeapEventName, isNotNil, Locale, NotificationType, PostSource } from '@malou-io/package-utils';

import { Config } from ':config';
import { CommentNotificationData } from ':modules/notifications/entities/comment-notification.entity';
import { InformationUpdateErrorNotificationData } from ':modules/notifications/entities/information-update-error-notification.entity';
import { MentionNotificationData } from ':modules/notifications/entities/mention-notification.entity';
import { MessageNotificationData } from ':modules/notifications/entities/message-notification.entity';
import { NewReviewsNotificationData } from ':modules/notifications/entities/new-reviews-notification.entity';
import { PostErrorNotificationData } from ':modules/notifications/entities/post-error-notification.entity';
import { PostSuggestionNotificationData } from ':modules/notifications/entities/post-suggestion-notification.entity';
import { SpecialHourNotificationData } from ':modules/notifications/entities/special-hour-notification.entity';
import {
    SummaryNotification,
    SummaryNotificationRestaurant,
    SummaryNotificationWithRestaurants,
} from ':modules/notifications/entities/summary-notification.entity';
import { Translation } from ':services/translation.service';

import { EmailNotificationPayload, NotificationMapper } from '../notification.mapper.interface';

interface SummaryNotificationEmailNotificationPayload extends EmailNotificationPayload {
    notification: SummaryNotification;
}

@singleton()
export class SummaryNotificationChannelsMapper
    implements NotificationMapper<SummaryNotificationWithRestaurants, SummaryNotificationEmailNotificationPayload, NotificationDTO, any>
{
    constructor(private readonly _translator: Translation) {}

    async mapToEmail(
        notification: SummaryNotificationWithRestaurants,
        apiKey: string
    ): Promise<SummaryNotificationEmailNotificationPayload> {
        const summaryNotificationTemplate = SummaryNotificationTemplate({
            locale: (notification.user.defaultLanguage as Locale) ?? Locale.FR,
            receiver: notification.user.name ?? '',
            allNotificationsLink: this._buildNotificationEmailLink(notification),
            unsubscribeLink: this._buildUnsubscribedLink(notification),
            trackingUrl: this._buildTrackingUrl({ apiKey, notificationId: notification.id, userEmail: notification.user.email }),
            notifications: this._getNotificationsEmailData(notification),
        });
        return {
            notification,
            emailSubject: this._getEmailSubject(notification),
            templateFunc: () => summaryNotificationTemplate,
            heapEventName: HeapEventName.NOTIFICATION_SUMMARY_TRACKING_EMAIL_SENT,
        };
    }

    async mapToPushNotification(_notification: SummaryNotification): Promise<any> {
        return null;
    }

    async mapToWeb(notification: SummaryNotification): Promise<NotificationDTO> {
        return notification.toDTO();
    }

    private _buildNotificationEmailLink(notification: SummaryNotification): string {
        return `${Config.baseAppUrl}/restaurants/${notification.data.restaurantIds[0]}?nchannel=email&type=${NotificationType.SUMMARY}&inner_type=${NotificationType.SUMMARY}&nid=${notification.id}`;
    }

    private _buildUnsubscribedLink(notification: SummaryNotification): string {
        return `${Config.baseAppUrl}/users/${notification.userId}/notifications-settings`;
    }

    private _buildTrackingUrl({
        apiKey,
        notificationId,
        userEmail,
    }: {
        apiKey: string;
        notificationId: string;
        userEmail: string;
    }): string {
        const urlWithoutQuery = `${Config.baseApiUrl}/notifications/emails/opened`;
        const today = new Date().getTime();
        return `${urlWithoutQuery}?nid=${notificationId}&receiverEmail=${userEmail}&t=${today}&api_key=${apiKey}&type=${NotificationType.SUMMARY}`;
    }

    private _getEmailSubject(notification: SummaryNotification): string {
        const emailLanguage = (notification.user.defaultLanguage as Locale) ?? Locale.FR;
        return notification.user.name
            ? this._translator.fromLang({ lang: emailLanguage }).notifications.summary.subject({
                  notificationsCount: notification.data.notifications?.length ?? notification.data.notificationIds.length,
                  receiverName: notification.user.name,
              })
            : this._translator.fromLang({ lang: emailLanguage }).notifications.summary.subject_without_name({
                  notificationsCount: notification.data.notifications?.length ?? notification.data.notificationIds.length,
              });
    }

    private _getNotificationsEmailData(notification: SummaryNotificationWithRestaurants): SummaryEmailNotificationProps['notifications'] {
        const locale = notification.user.defaultLanguage as Locale;
        return notification.data.notifications
            ?.map((notif) => {
                switch (notif.type) {
                    case NotificationType.SPECIAL_HOUR:
                        return {
                            type: NotificationType.SPECIAL_HOUR,
                            data: this._getSpecialHourEmailData(notification.id, notif.data as SpecialHourNotificationData, locale),
                        };
                    case NotificationType.POST_SUGGESTION:
                        return {
                            type: NotificationType.POST_SUGGESTION,
                            data: this._getPostSuggestionEmailData(notification.id, notif.data as PostSuggestionNotificationData, locale),
                        };
                    case NotificationType.REVIEW:
                        return {
                            type: NotificationType.REVIEW,
                            data: this._getReviewEmailData(
                                notification.id,
                                notif.data as NewReviewsNotificationData,
                                notification.data.restaurants
                            ),
                        };
                    case NotificationType.POST_ERROR:
                        return {
                            type: NotificationType.POST_ERROR,
                            data: this._getPostErrorEmailData(
                                notification.id,
                                notif.data as PostErrorNotificationData,
                                notification.data.restaurants
                            ),
                        };
                    case NotificationType.NEW_MESSAGE:
                        return {
                            type: NotificationType.NEW_MESSAGE,
                            data: this._getMessageEmailData(
                                notification.id,
                                notif.data as MessageNotificationData,
                                notification.data.restaurants
                            ),
                        };
                    case NotificationType.COMMENT:
                        return {
                            type: NotificationType.COMMENT,
                            data: this._getCommentEmailData(
                                notification.id,
                                notif.data as CommentNotificationData,
                                notification.data.restaurants
                            ),
                        };
                    case NotificationType.MENTION:
                        return {
                            type: NotificationType.MENTION,
                            data: this._getMentionEmailData(
                                notification.id,
                                notif.data as MentionNotificationData,
                                notification.data.restaurants
                            ),
                        };
                    case NotificationType.INFORMATION_UPDATE_ERROR:
                        return {
                            type: NotificationType.INFORMATION_UPDATE_ERROR,
                            data: this._getInformationUpdateErrorEmailData(
                                notification.id,
                                notif.data as InformationUpdateErrorNotificationData
                            ),
                        };
                    default:
                        return null;
                }
            })
            .filter(isNotNil);
    }

    private _getInformationUpdateErrorEmailData(
        notificationId: string,
        data: InformationUpdateErrorNotificationData
    ): SummaryInformationUpdateErrorProps {
        const restaurantId = data.restaurantIds[0];
        const link = restaurantId
            ? `${Config.baseAppUrl}/restaurants/${restaurantId}/seo/informations?utm_source=email&type=${NotificationType.SUMMARY}` +
              `&inner_type=${NotificationType.INFORMATION_UPDATE_ERROR}&nid=${notificationId}&nchannel=email`
            : undefined;
        return {
            link,
            restaurantName: data.mainRestaurantName,
        };
    }

    private _getSpecialHourEmailData(notificationId: string, data: SpecialHourNotificationData, locale: Locale): SummarySpecialHourProps {
        const restaurantId = data.restaurantIds[0];
        const link = restaurantId
            ? `${Config.baseAppUrl}/restaurants/${restaurantId}/seo/informations?nchannel=email&type=${NotificationType.SUMMARY}` +
              `&inner_type=${NotificationType.SPECIAL_HOUR}&nid=${notificationId}`
            : undefined;

        return {
            emoji: data.event.emoji,
            name: data.event.name[locale] ?? '',
            startDate: data.event.startDate ?? new Date(),
            link,
        };
    }

    private _getPostSuggestionEmailData(
        notificationId: string,
        data: PostSuggestionNotificationData,
        locale: Locale
    ): SummaryPostSuggestionProps {
        const restaurantId = data.restaurantIds[0];
        const url = `${Config.baseAppUrl}/restaurants/${restaurantId}/seo/posts/list`;
        const query = `nchannel=email&type=${NotificationType.SUMMARY}&inner_type=${NotificationType.POST_SUGGESTION}&nid=${notificationId}`;
        const link = restaurantId ? `${url}?${query}` : undefined;

        return {
            emoji: data.event.emoji,
            name: data.event.name[locale] ?? '',
            startDate: DateTime.fromObject(data.event.date).toJSDate(),
            ideas: data.event.ideas?.[locale] ?? '',
            link,
        };
    }

    private _getReviewEmailData(
        notificationId: string,
        data: NewReviewsNotificationData,
        restaurants: SummaryNotificationRestaurant
    ): SummaryReviewProps {
        const firstRestaurantName = restaurants?.[data.restaurantIds[0]]?.name;
        const restaurantsCount = data.restaurantIds.length;
        const link =
            restaurantsCount > 1
                ? `${Config.baseAppUrl}/groups/reputation/reviews?nchannel=email&type=${NotificationType.SUMMARY}&inner_type=${NotificationType.REVIEW}&nid=${notificationId}`
                : `${Config.baseAppUrl}/restaurants/${data.restaurantIds[0]}/reputation/reviews?nchannel=email&type=${NotificationType.SUMMARY}&inner_type=${NotificationType.REVIEW}&nid=${notificationId}`;

        return {
            count: data.reviewIds.length,
            restaurantCount: data.restaurantIds.length,
            firstRestaurantName: firstRestaurantName,
            link,
        };
    }

    private _getPostErrorEmailData(
        notificationId: string,
        data: PostErrorNotificationData,
        restaurants: SummaryNotificationRestaurant
    ): SummaryPostErrorProps {
        const post = data.post;
        const restaurant = post.restaurantId ? restaurants[post.restaurantId] : undefined;
        assert(restaurant, 'Restaurant not found');
        const restaurantName = restaurant.name;
        const postSourceMapping = {
            [PostSource.SEO]: 'seo/posts/list',
            [PostSource.SOCIAL]: 'social/socialposts',
        };
        const link = `${Config.baseAppUrl}/restaurants/${post.restaurantId}/${postSourceMapping[post.source]}?postId=${
            post.id
        }&nchannel=email&type=${NotificationType.SUMMARY}&inner_type=${NotificationType.POST_ERROR}&nid=${notificationId}`;

        return {
            restaurantName,
            postImgUrl: data.post.attachments.igFit,
            link,
        };
    }

    private _getMessageEmailData(
        notificationId: string,
        data: MessageNotificationData,
        restaurants: SummaryNotificationRestaurant
    ): SummaryMessageProps {
        const restaurantId = data.conversations[0]?.restaurantId;
        const link = restaurantId
            ? `${Config.baseAppUrl}/restaurants/${restaurantId}/interactions/messages?nchannel=email&type=${NotificationType.SUMMARY}&inner_type=${NotificationType.NEW_MESSAGE}&nid=${notificationId}`
            : undefined;

        const [conversation] = data.conversations;
        return {
            conversation: {
                ...conversation,
                restaurantName: restaurants[conversation.restaurantId].name,
                messages: conversation.messages.map((message) => ({
                    text: message.text,
                    senderName: message.senderName,
                })),
            },
            messageCount: data.messageCount,
            restaurantCount: data.restaurantIds.length,
            link,
        };
    }

    private _getCommentEmailData(
        notificationId: string,
        data: CommentNotificationData,
        restaurants: SummaryNotificationRestaurant
    ): SummaryCommentProps {
        const restaurantId = data.comments[0]?.restaurantId;
        const link = restaurantId
            ? `${Config.baseAppUrl}/restaurants/${restaurantId}/interactions/comments?nchannel=email&type=${NotificationType.SUMMARY}&inner_type=${NotificationType.COMMENT}&nid=${notificationId}`
            : undefined;

        const [comment] = data.comments;
        return {
            comment: {
                text: comment.text,
                authorDisplayName: comment.authorDisplayName,
                restaurantName: restaurants[comment.restaurantId].name,
            },
            commentCount: data.commentCount,
            restaurantCount: data.restaurantIds.length,
            link,
        };
    }

    private _getMentionEmailData(
        notificationId: string,
        data: MentionNotificationData,
        restaurants: SummaryNotificationRestaurant
    ): SummaryMentionProps {
        const restaurantId = data.mentions[0]?.restaurantId;
        const link = restaurantId
            ? `${Config.baseAppUrl}/restaurants/${restaurantId}/interactions/comments?nchannel=email&type=${NotificationType.SUMMARY}&inner_type=${NotificationType.MENTION}&nid=${notificationId}`
            : undefined;

        const [mention] = data.mentions;

        return {
            mention: {
                text: mention.text,
                authorDisplayName: mention.authorDisplayName ?? undefined,
                restaurantName: restaurants[mention.restaurantId].name,
            },
            mentionCount: data.mentionCount,
            restaurantCount: data.restaurantIds.length,
            link,
        };
    }
}

import { EntityConstructor, SpecialHourNotificationAction } from '@malou-io/package-utils';

import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';
import { NotificationUserProps } from ':modules/notifications/entities/child-entities/notification-user.entity';

import { Notification } from './notification.entity';

export type SpecialHourNotificationEventData = Pick<CalendarEvent, 'id' | 'emoji' | 'name'> & {
    startDate: Date;
    action: SpecialHourNotificationAction;
};

export interface SpecialHourNotificationData {
    restaurantIds: string[];
    event: SpecialHourNotificationEventData;
}

type SpecialHourNotificationProps = EntityConstructor<SpecialHourNotification, { user: NotificationUserProps }>;

export class SpecialHourNotification extends Notification<SpecialHourNotificationData> {
    constructor(props: SpecialHourNotificationProps) {
        super(props);
    }
}

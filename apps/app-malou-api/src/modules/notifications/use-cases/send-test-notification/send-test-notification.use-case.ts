import assert from 'assert';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { <PERSON><PERSON><PERSON>h<PERSON>ear, InformationUpdatePlatformStateStatus, NotificationChannel, NotificationType } from '@malou-io/package-utils';

import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import { InformationUpdatesRepository } from ':modules/information-updates/information-updates.repository';
import { ConversationsRepository, MessagesRepository } from ':modules/messages/messages.repository';
import { NotificationReviewsRepository } from ':modules/notifications/repositories/notifications-reviews.repository';
import { CreateDailyMessageNotificationUseCase } from ':modules/notifications/use-cases/create-daily-message-notification/create-daily-message-notification.use-case';
import { CreateInfoUpdateErrorNotificationUseCase } from ':modules/notifications/use-cases/create-info-update-error-notification/create-info-update-error-notification.use-case';
import { CreateNegativeReviewsNotificationsUseCase } from ':modules/notifications/use-cases/create-negative-reviews-notifications/create-negative-reviews-notifications.use-case';
import { CreateNewMessageNotificationUseCase } from ':modules/notifications/use-cases/create-new-message-notification/create-new-message-notification.use-case';
import { CreateNewReviewsNotificationUseCase } from ':modules/notifications/use-cases/create-new-reviews-notification/create-new-reviews-notification.use-case';
import { CreatePostReminderNotificationUseCase } from ':modules/notifications/use-cases/create-post-reminder-notification/create-post-reminder-notification.use-case';
import { CreatePostsSuggestionNotificationUseCase } from ':modules/notifications/use-cases/create-posts-suggestion-notification/create-posts-suggestion-notification.use-case';
import { CreateRoiActivatedNotificationUseCase } from ':modules/notifications/use-cases/create-roi-activated-notification/create-roi-activated-notification.use-case';
import { CreateSpecialHourNotificationUseCase } from ':modules/notifications/use-cases/create-special-hour-notification/create-special-hour-notification.use-case';
import { CreateSummaryNotificationsUseCase } from ':modules/notifications/use-cases/create-summary-notifications/create-summary-notifications.use-case';
import { UserRestaurantsRepository } from ':modules/user-restaurants/user-restaurants.repository';

@singleton()
export default class SendTestNotificationUseCase {
    // This usecase is only for testing purpose for admins, that's why i import other usecases here
    constructor(
        private readonly _createNewReviewsNotificationUseCase: CreateNewReviewsNotificationUseCase,
        private readonly _createNegativeReviewNotificationsUseCase: CreateNegativeReviewsNotificationsUseCase,
        private readonly _createSpecialHourNotificationUseCase: CreateSpecialHourNotificationUseCase,
        private readonly _calendarEventsRepository: CalendarEventsRepository,
        private readonly _createPostsSuggestionNotificationUseCase: CreatePostsSuggestionNotificationUseCase,
        private readonly _reviewsRepository: NotificationReviewsRepository,
        private readonly _userRestaurantsRepository: UserRestaurantsRepository,
        private readonly _createRoiActivatedNotificationUseCase: CreateRoiActivatedNotificationUseCase,
        private readonly _messagesRepository: MessagesRepository,
        private readonly _conversationsRepository: ConversationsRepository,
        private readonly _createMessageNotificationUseCase: CreateNewMessageNotificationUseCase,
        private readonly _createSummaryNotificationsUseCase: CreateSummaryNotificationsUseCase,
        private readonly _createInformationUpdateErrorNotificationUseCase: CreateInfoUpdateErrorNotificationUseCase,
        private readonly _createPostsReminderNotificationUseCase: CreatePostReminderNotificationUseCase,
        private readonly _informationUpdatesRepository: InformationUpdatesRepository,
        private readonly _createDailyMessageNotificationUseCase: CreateDailyMessageNotificationUseCase
    ) {}

    async execute({ notificationType, userId }: { notificationType: string; userId: string }) {
        switch (notificationType) {
            case NotificationType.REVIEW:
                const userRestaurants1 = await this._userRestaurantsRepository.find({ filter: { userId } });
                const restaurantsWithReviews = await Promise.all(
                    userRestaurants1.map((userRestaurant) =>
                        this._reviewsRepository.findReviewsForRestaurant(userRestaurant.restaurantId.toString())
                    )
                ).then((restaurantReviews) => restaurantReviews.filter((reviews) => reviews.length > 0));
                const randomRestaurantReviews = restaurantsWithReviews[Math.floor(Math.random() * restaurantsWithReviews.length)];
                if (!randomRestaurantReviews || !randomRestaurantReviews.length) return;
                await this._createNewReviewsNotificationUseCase.execute(
                    randomRestaurantReviews.map((review) => review._id.toString()),
                    userId
                );
                return;
            case NotificationType.SPECIAL_HOUR:
                const upcomingEvents = await this._calendarEventsRepository.getEventsWithSpecialHoursSuggestion(
                    { startDate: DateTime.now().toObject() as DayMonthYear }, // todo remove 'as' when upgrading luxon
                    10
                );

                await this._createSpecialHourNotificationUseCase.execute(upcomingEvents, NotificationChannel.EMAIL, userId);
                await this._createSpecialHourNotificationUseCase.execute(upcomingEvents, NotificationChannel.WEB, userId);
                return;
            case NotificationType.REVIEW_REPLY_REMINDER:
                await this._createNegativeReviewNotificationsUseCase.execute(userId);
                return;
            case NotificationType.POST_SUGGESTION:
                const upcomingPostsEvents = await this._calendarEventsRepository.getEventsWithPostSuggestion({
                    startDate: DateTime.now().toObject() as DayMonthYear, // todo remove 'as' when upgrading luxon
                    endDate: DateTime.now().plus({ days: 14 }).toObject() as DayMonthYear, // todo remove 'as' when upgrading luxon
                });
                await this._createPostsSuggestionNotificationUseCase.execute({
                    calendarEvents: upcomingPostsEvents,
                    testUserId: userId,
                    channel: NotificationChannel.EMAIL,
                });
                await this._createPostsSuggestionNotificationUseCase.execute({
                    calendarEvents: upcomingPostsEvents,
                    testUserId: userId,
                    channel: NotificationChannel.WEB,
                });
                return;
            case NotificationType.ROI_ACTIVATED:
                const UserRestaurants = (await this._userRestaurantsRepository.find({ filter: { userId } })).slice(0, 5);
                const restaurantIds = UserRestaurants.map((restaurant) => restaurant.restaurantId.toString());
                await this._createRoiActivatedNotificationUseCase.execute(restaurantIds, userId);
                await this._createRoiActivatedNotificationUseCase.execute([restaurantIds[0]], userId);
                return;
            case NotificationType.NEW_MESSAGE:
                const userRestaurants = (
                    await this._userRestaurantsRepository.find({
                        filter: { userId },
                        options: { lean: true, populate: [{ path: 'restaurant' }] },
                    })
                )
                    .filter((ur) => ur.restaurant?.active)
                    .slice(0, 5);
                const conversations = await this._conversationsRepository.find({
                    filter: { restaurantId: { $in: userRestaurants.map((restaurant) => restaurant.restaurantId.toString()) } },
                    options: { lean: true },
                });
                const messages = await this._messagesRepository.find({
                    filter: { conversationId: { $in: conversations.map((conversation) => conversation._id.toString()) } },
                    options: { lean: true },
                });
                const randomMessage = messages[Math.floor(Math.random() * messages.length)];

                await this._createMessageNotificationUseCase.execute({ messageId: randomMessage._id.toString(), testUserId: userId });
                return;
            case NotificationType.SUMMARY:
                await this._createSummaryNotificationsUseCase.execute(userId);
                return;
            case NotificationType.INFORMATION_UPDATE_ERROR:
                const infoUpdateId = await this._informationUpdatesRepository
                    .findOne({
                        filter: {
                            'platformStates.status': InformationUpdatePlatformStateStatus.ERROR,
                        },
                        options: { lean: true },
                    })
                    .then((infoUpdate) => infoUpdate?._id.toString());
                assert(infoUpdateId, '[SendTestNotificationUseCase] No information update found');
                await this._createInformationUpdateErrorNotificationUseCase.execute({
                    infoUpdateId,
                    testUserId: userId,
                });
                return;

            case NotificationType.NO_MORE_SCHEDULED_POSTS:
                await this._createPostsReminderNotificationUseCase.execute(userId);
                return;
            case NotificationType.DAILY_UNREAD_MESSAGES_NOTIFICATION:
                await this._createDailyMessageNotificationUseCase.execute(userId);
                return;
            default:
                throw new Error('Notification type not supported');
        }
    }
}

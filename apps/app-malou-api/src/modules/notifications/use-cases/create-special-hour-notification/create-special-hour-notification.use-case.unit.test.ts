import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import {
    BusinessCategory,
    CalendarEventCountry,
    CaslRole,
    NotificationChannel,
    RestaurantCalendarEventsCountry,
} from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';
import { getDefaultCalendarEvent } from ':modules/calendar-events/tests/calendar-event.builder';
import NotificationsRepository from ':modules/notifications/repositories/notifications.repository';
import { NotificationSenderService } from ':modules/notifications/services/notifications-sender-service/notification-sender.service.interface';
import { CreateSpecialHourNotificationUseCase } from ':modules/notifications/use-cases/create-special-hour-notification/create-special-hour-notification.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { MOCKED_NOW_DATE_TIME } from ':modules/roi/tests/roi.constants';
import { defaultUserSettings, getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';
import { UsersRepository } from ':modules/users/users.repository';
import * as experimentationModule from ':services/experimentations-service/experimentation.service';

describe('CreateSpecialHourNotificationUseCase', () => {
    let baseTestCase: TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'notifications' | 'calendarEvents'>;
    beforeAll(() => {
        registerRepositories([
            'NotificationsRepository',
            'UserRestaurantsRepository',
            'CalendarEventsRepository',
            'UsersRepository',
            'RestaurantsRepository',
        ]);

        container.register<NotificationSenderService>(InjectionToken.NotificationSenderService, {
            useValue: {
                sendNotificationsToChannels: async (_) => void 0,
            },
        });

        const spy = jest.spyOn(experimentationModule, 'isFeatureAvailableForUser');
        spy.mockImplementation(() => Promise.resolve(true));

        const mockDateNow = jest.fn(() => MOCKED_NOW_DATE_TIME);
        global.Date.now = mockDateNow;
    });

    beforeEach(async () => {
        baseTestCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'calendarEvents' | 'notifications'>({
            seeds: {
                calendarEvents: {
                    data() {
                        return [
                            getDefaultCalendarEvent()
                                .name({ en: 'Event 0', fr: 'Événement 0' })
                                .country(CalendarEventCountry.FRANCE)
                                .build(),
                            getDefaultCalendarEvent()
                                .name({ en: 'Event 1', fr: 'Événement 1' })
                                .country(CalendarEventCountry.FRANCE)
                                .build(),
                            getDefaultCalendarEvent()
                                .name({ en: 'Event 2', fr: 'Événement 2' })
                                .country(CalendarEventCountry.FRANCE)
                                .build(),
                            getDefaultCalendarEvent()
                                .name({ en: 'Event 3', fr: 'Événement 3' })
                                .country(CalendarEventCountry.FRANCE)
                                .build(),
                        ];
                    },
                },
                restaurants: {
                    data(deps) {
                        return [
                            getDefaultRestaurant()
                                .name('restaurant_0')
                                .uniqueKey('restaurant_0')
                                .internalName('restaurant_0')
                                .calendarEvents([
                                    deps.calendarEvents()[0]._id,
                                    deps.calendarEvents()[1]._id,
                                    deps.calendarEvents()[2]._id,
                                    deps.calendarEvents()[3]._id,
                                ])
                                .build(),
                            getDefaultRestaurant()
                                .calendarEvents([deps.calendarEvents()[1]._id, deps.calendarEvents()[2]._id, deps.calendarEvents()[3]._id])
                                .name('restaurant_1')
                                .uniqueKey('restaurant_1')
                                .internalName('restaurant_1')
                                .build(),
                            getDefaultRestaurant()
                                .calendarEvents([deps.calendarEvents()[0]._id, deps.calendarEvents()[3]._id])
                                .name('restaurant_2')
                                .uniqueKey('restaurant_2')
                                .internalName('restaurant_2')
                                .build(),
                            getDefaultRestaurant()
                                .calendarEvents([deps.calendarEvents()[2]._id, deps.calendarEvents()[3]._id])
                                .name('restaurant_3')
                                .uniqueKey('restaurant_3')
                                .internalName('restaurant_3')
                                .build(),
                            getDefaultRestaurant()
                                .calendarEvents([deps.calendarEvents()[0]._id, deps.calendarEvents()[1]._id])
                                .name('restaurant_4')
                                .calendarEventsCountry(RestaurantCalendarEventsCountry.MOROCCO)
                                .uniqueKey('restaurant_4')
                                .internalName('restaurant_4')
                                .build(),
                        ];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),

                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                            getDefaultUser()
                                .name('user_2')
                                .email('<EMAIL>')
                                .settings({
                                    ...defaultUserSettings,
                                    notifications: {
                                        ...defaultUserSettings.notifications,
                                        email: {
                                            ...defaultUserSettings.notifications.email,
                                            specialHourReminder: { active: false },
                                        },
                                        web: {
                                            ...defaultUserSettings.notifications.web,
                                            specialHourReminder: { active: false },
                                        },
                                    },
                                })
                                .build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .caslRole(CaslRole.EDITOR)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[4]._id)
                                .caslRole(CaslRole.EDITOR)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[2]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[2]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[2]._id)
                                .restaurantId(dependencies.restaurants()[3]._id)
                                .caslRole(CaslRole.EDITOR)
                                .build(),
                        ];
                    },
                },
                notifications: {
                    data() {
                        return [];
                    },
                },
            },
            expectedResult(_dependencies) {
                return [];
            },
        });

        await baseTestCase.build();
    });

    describe('execute', () => {
        const notificationsRepository = container.resolve(NotificationsRepository);
        const restaurantsRepository = container.resolve(RestaurantsRepository);
        const usersRepository = container.resolve(UsersRepository);

        it('should create 2 notifications for USER_0 and 1 notification for USER_1', async () => {
            const usecase = container.resolve(CreateSpecialHourNotificationUseCase);
            const e0 = baseTestCase.getSeededObjects().calendarEvents[0];
            const e1 = baseTestCase.getSeededObjects().calendarEvents[1];
            const calendarEvents = [
                new CalendarEvent({ id: e0._id.toString(), ...e0 }),
                new CalendarEvent({ id: e1._id.toString(), ...e1 }),
            ];

            await usecase.execute(calendarEvents, NotificationChannel.EMAIL);

            const notifications = await notificationsRepository.find({ filter: {}, options: { lean: true } });
            const seedeObjects = baseTestCase.getSeededObjects();
            const userIds = [seedeObjects.users[0]._id.toString(), seedeObjects.users[1]._id.toString()];

            expect(notifications).toHaveLength(3);
            expect(notifications.map((notification) => notification.channel)).toIncludeAllMembers([NotificationChannel.EMAIL]);
            expect(notifications.map((notification) => notification.userId.toString())).toIncludeAllMembers(userIds);
        });

        it('should not create notifications for brand restaurants', async () => {
            const usecase = container.resolve(CreateSpecialHourNotificationUseCase);
            await restaurantsRepository.findOneAndUpdate({
                filter: { internalName: 'restaurant_2' },
                update: { type: BusinessCategory.BRAND },
            });

            const e0 = baseTestCase.getSeededObjects().calendarEvents[0];
            const e1 = baseTestCase.getSeededObjects().calendarEvents[1];
            const calendarEvents = [
                new CalendarEvent({ id: e0._id.toString(), ...e0 }),
                new CalendarEvent({ id: e1._id.toString(), ...e1 }),
            ];

            await usecase.execute(calendarEvents, NotificationChannel.EMAIL);

            const notifications = await notificationsRepository.find({ filter: {}, options: { lean: true } });
            const seedeObjects = baseTestCase.getSeededObjects();
            const userIds = [seedeObjects.users[0]._id.toString()];

            expect(notifications).toHaveLength(2);
            expect(notifications.map((notification) => notification.channel)).toIncludeAllMembers([NotificationChannel.EMAIL]);
            expect(notifications.map((notification) => notification.userId.toString())).toIncludeAllMembers(userIds);
        });

        it('should not create notifications for USER_2', async () => {
            const usecase = container.resolve(CreateSpecialHourNotificationUseCase);
            const e0 = baseTestCase.getSeededObjects().calendarEvents[0];
            const e1 = baseTestCase.getSeededObjects().calendarEvents[1];
            const calendarEvents = [
                new CalendarEvent({ id: e0._id.toString(), ...e0 }),
                new CalendarEvent({ id: e1._id.toString(), ...e1 }),
            ];

            await usecase.execute(calendarEvents, NotificationChannel.EMAIL);

            const notifications = await notificationsRepository.find({ filter: {} });

            expect(
                notifications.map((notification) => notification.userId).filter((id) => id === baseTestCase.getSeededObjects().users[2]._id)
            ).toHaveLength(0);
        });

        it('should not create notifications if restaurants are inactive', async () => {
            const usecase = container.resolve(CreateSpecialHourNotificationUseCase);
            await restaurantsRepository.updateMany({ filter: {}, update: { active: false } });

            const e0 = baseTestCase.getSeededObjects().calendarEvents[0];
            const e1 = baseTestCase.getSeededObjects().calendarEvents[1];
            const calendarEvents = [
                new CalendarEvent({ id: e0._id.toString(), ...e0 }),
                new CalendarEvent({ id: e1._id.toString(), ...e1 }),
            ];

            await usecase.execute(calendarEvents, NotificationChannel.EMAIL);

            const notifications = await notificationsRepository.find({ filter: {} });

            expect(notifications).toHaveLength(0);
        });

        it('should not create notifications for USER_0 because his notifications settings are inactive', async () => {
            const usecase = container.resolve(CreateSpecialHourNotificationUseCase);
            await usersRepository.findOneAndUpdate({
                filter: { name: 'user_0' },
                update: {
                    settings: {
                        ...defaultUserSettings,
                        notifications: {
                            ...defaultUserSettings.notifications,
                            email: {
                                ...defaultUserSettings.notifications.email,
                                specialHourReminder: { active: false },
                            },
                            web: {
                                ...defaultUserSettings.notifications.web,
                                specialHourReminder: { active: false },
                            },
                        },
                    },
                },
            });
            const e0 = baseTestCase.getSeededObjects().calendarEvents[0];
            const e1 = baseTestCase.getSeededObjects().calendarEvents[1];
            const calendarEvents = [
                new CalendarEvent({ id: e0._id.toString(), ...e0 }),
                new CalendarEvent({ id: e1._id.toString(), ...e1 }),
            ];

            await usecase.execute(calendarEvents, NotificationChannel.EMAIL);

            const notifications = await notificationsRepository.find({ filter: {} });

            expect(notifications).toHaveLength(1);
        });

        it('should not create notifications for USER_1 because special hours are defined on the same day', async () => {
            const usecase = container.resolve(CreateSpecialHourNotificationUseCase);
            const eventDate = DateTime.now().plus({ days: 10 });
            await restaurantsRepository.findOneAndUpdate({
                filter: { uniqueKey: 'restaurant_2' },
                update: {
                    specialHours: [
                        {
                            isClosed: false,
                            startDate: {
                                year: eventDate.year,
                                month: eventDate.month - 1, // javascript month is 0 indexed
                                day: eventDate.day,
                            },
                            endDate: {
                                year: eventDate.year,
                                month: eventDate.month - 1,
                                day: eventDate.day,
                            },
                            closeTime: '17:00',
                            openTime: '09:00',
                        },
                    ],
                },
            });
            const e0 = baseTestCase.getSeededObjects().calendarEvents[0];
            const calendarEvents = [new CalendarEvent({ id: e0._id.toString(), ...e0 })];

            await usecase.execute(calendarEvents, NotificationChannel.EMAIL);

            const notifications = await notificationsRepository.find({ filter: {} });

            expect(notifications).toHaveLength(2);
        });

        it('should not create notifications if there are no events', async () => {
            const usecase = container.resolve(CreateSpecialHourNotificationUseCase);
            await usecase.execute([], NotificationChannel.EMAIL);
            const notifications = await notificationsRepository.find({ filter: {} });
            expect(notifications).toHaveLength(0);
        });
    });
});

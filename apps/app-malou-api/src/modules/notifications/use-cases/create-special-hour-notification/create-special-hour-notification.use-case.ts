import { groupBy } from 'lodash';
import { DateTime } from 'luxon';
import { inject, registry, singleton } from 'tsyringe';

import { IRestaurant, newDbId } from '@malou-io/package-models';
import {
    CalendarEventCountry,
    CalendarEventCountryType,
    DayMonthYear,
    isNotNil,
    isSameDay,
    NotificationChannel,
    NOTIFICATIONS_ROLE,
    NotificationType,
    RestaurantCalendarEventsCountryType,
    SpecialHourNotificationAction,
} from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';
import { NotificationUser } from ':modules/notifications/entities/child-entities/notification-user.entity';
import { SpecialHourNotification } from ':modules/notifications/entities/special-hour-notification.entity';
import { NotificationsUserRestaurantsRepository } from ':modules/notifications/repositories/notifications-user-restaurants/notifications-user-restaurants.repository';
import NotificationsRepository from ':modules/notifications/repositories/notifications.repository';
import { NotificationSenderService } from ':modules/notifications/services/notifications-sender-service/notification-sender.service.interface';
import { SendNotificationsToChannelsService } from ':modules/notifications/services/notifications-sender-service/send-notifications-to-channels.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { isFeatureAvailableForUsers } from ':services/experimentations-service/experimentation.service';

interface UserWithRestaurants {
    user: NotificationUser;
    restaurantsIds: string[];
}
@singleton()
@registry([
    {
        token: InjectionToken.NotificationSenderService,
        useClass: SendNotificationsToChannelsService,
    },
])
export class CreateSpecialHourNotificationUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _notificationsUserRestaurantsRepository: NotificationsUserRestaurantsRepository,
        private readonly _notificationsRepository: NotificationsRepository,
        @inject(InjectionToken.NotificationSenderService)
        private readonly _sendNotificationsToChannelsService: NotificationSenderService
    ) {}

    async execute(calendarEvents: CalendarEvent[], channel: NotificationChannel, testUserId?: string): Promise<void> {
        if (calendarEvents.length === 0) {
            return;
        }

        for (const calendarEvent of calendarEvents) {
            const restaurantsIds = (await this._restaurantsRepository.getRestaurantsEventCountryAndSpecialHoursByEventId(calendarEvent.id))
                .filter(
                    (restaurant) =>
                        this._isSameCalendarEventCountry(calendarEvent.country, restaurant.calendarEventsCountry) &&
                        calendarEvent.date &&
                        !this._isRestaurantSpecialHoursDefined(restaurant.specialHours, calendarEvent.date)
                )
                .map((restaurant) => restaurant.id);

            const restaurantUsers = await Promise.all(
                restaurantsIds.map(async (restaurantId) =>
                    (
                        await this._notificationsUserRestaurantsRepository.getActiveUserRestaurantsByRestaurantId(
                            restaurantId,
                            NOTIFICATIONS_ROLE[NotificationType.SPECIAL_HOUR],
                            testUserId
                        )
                    ).map((userRestaurant) => ({ user: userRestaurant.user, restaurantId }))
                )
            );
            let flattenedRestaurantUsers = groupBy(restaurantUsers.flat(), 'user.id');
            const usersForWhichToCreateNotification: UserWithRestaurants[] = [];
            const featureEnabledForUsers = await isFeatureAvailableForUsers({
                userIds: Object.keys(flattenedRestaurantUsers),
                featureName: 'release-special-hour-email-notifications',
            });

            flattenedRestaurantUsers = Object.fromEntries(
                Object.entries(flattenedRestaurantUsers).filter(([userId]) => featureEnabledForUsers.includes(userId))
            );
            for (const restaurantUser of Object.values(flattenedRestaurantUsers)) {
                const restaurantIds = restaurantUser.map((restaurant) => restaurant.restaurantId);
                usersForWhichToCreateNotification.push({
                    user: restaurantUser[0].user,
                    restaurantsIds: restaurantIds,
                });
            }
            const specialHourNotificationsToCreate: SpecialHourNotification[] = [];
            usersForWhichToCreateNotification
                .filter(
                    (userWithRestaurant) =>
                        userWithRestaurant.user.getReceivableNotificationChannels(NotificationType.SPECIAL_HOUR).length > 0 &&
                        (!testUserId || userWithRestaurant.user.id === testUserId)
                )
                .map((userWithRestaurant) => {
                    const user = userWithRestaurant.user;
                    const userHasNotificationEnabled = user.hasActivatedNotificationForChannel(NotificationType.SPECIAL_HOUR, channel);
                    if (!calendarEvent.date) {
                        logger.error('[CreateSpecialHourNotificationUseCase] Calendar event start date is undefined', {
                            calendarEventId: calendarEvent.id,
                        });
                        return null;
                    }
                    if (userHasNotificationEnabled) {
                        const notificationId = newDbId().toString();
                        const specialHourNotification = new SpecialHourNotification({
                            channel,
                            userId: user.id,
                            id: notificationId,
                            type: NotificationType.SPECIAL_HOUR,
                            createdAt: new Date(),
                            restaurantId: undefined,
                            user,
                            data: {
                                event: {
                                    id: calendarEvent.id,
                                    emoji: calendarEvent.emoji,
                                    startDate: DateTime.fromObject(calendarEvent.date).toJSDate(),
                                    name: calendarEvent.name,
                                    action: SpecialHourNotificationAction.NOTHING,
                                },
                                restaurantIds: userWithRestaurant.restaurantsIds,
                            },
                        });
                        specialHourNotificationsToCreate.push(specialHourNotification);
                    }
                })
                .filter(isNotNil);
            await this._notificationsRepository.createManySpecialHourNotifications(specialHourNotificationsToCreate);
            await this._sendNotificationsToChannelsService.sendNotificationsToChannels(specialHourNotificationsToCreate.flat());
        }
    }

    private _isSameCalendarEventCountry(
        calendarEventCountry: CalendarEventCountryType,
        restaurantCountry: RestaurantCalendarEventsCountryType | undefined
    ): boolean {
        if (calendarEventCountry === CalendarEventCountry.ALL) {
            return true;
        }
        if (!restaurantCountry) {
            return calendarEventCountry === CalendarEventCountry.FRANCE;
        }
        return calendarEventCountry === restaurantCountry;
    }

    private _isRestaurantSpecialHoursDefined(specialHours: IRestaurant['specialHours'], eventStartDate: DayMonthYear): boolean {
        return specialHours.some((specialHour) => {
            const specialHourStartDate = DateTime.fromObject({
                day: specialHour.startDate.day,
                month: specialHour.startDate.month + 1,
                year: specialHour.startDate.year,
                zone: 'utc',
            }).toJSDate();
            return isSameDay(DateTime.fromObject(eventStartDate).toJSDate(), specialHourStartDate);
        });
    }
}

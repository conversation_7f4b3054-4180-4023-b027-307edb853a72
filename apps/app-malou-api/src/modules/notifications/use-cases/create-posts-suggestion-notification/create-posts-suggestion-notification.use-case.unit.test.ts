import { container } from 'tsyringe';

import { CaslRole, NotificationChannel, NotificationType, RestaurantCalendarEventsCountry } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';
import { getDefaultCalendarEvent } from ':modules/calendar-events/tests/calendar-event.builder';
import NotificationsRepository from ':modules/notifications/repositories/notifications.repository';
import { NotificationSenderService } from ':modules/notifications/services/notifications-sender-service/notification-sender.service.interface';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';

import { CreatePostsSuggestionNotificationUseCase } from './create-posts-suggestion-notification.use-case';

describe('CreatePostsSuggestionNotificationUseCase', () => {
    beforeAll(() => {
        registerRepositories(['UsersRepository', 'RestaurantsRepository', 'UserRestaurantsRepository', 'NotificationsRepository']);

        container.register<NotificationSenderService>(InjectionToken.NotificationSenderService, {
            useValue: {
                sendNotificationsToChannels: async (_) => void 0,
            },
        });
    });

    let testcases: TestCaseBuilderV2<'restaurants' | 'userRestaurants' | 'users'>;

    beforeEach(async () => {
        testcases = new TestCaseBuilderV2<'restaurants' | 'userRestaurants' | 'users'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                .name('restaurant_0')
                                .calendarEventsCountry(RestaurantCalendarEventsCountry.FRANCE)
                                .build(),
                            getDefaultRestaurant()
                                .name('restaurant_1')
                                .calendarEventsCountry(RestaurantCalendarEventsCountry.UNITED_STATES)
                                .build(),
                        ];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                            getDefaultUser().name('user_2').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),

                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .caslRole(CaslRole.EDITOR)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),

                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .caslRole(CaslRole.EDITOR)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: () => [],
        });

        await testcases.build();
    });
    describe('CreatePostsSuggestionNotificationUseCase.execute', () => {
        const notificationsRepository = container.resolve(NotificationsRepository);

        it('should create 2 notifications (Web) for each user with restaurants in the same country as the event for an upcoming event in 14 days', async () => {
            const usecase = container.resolve(CreatePostsSuggestionNotificationUseCase);
            const calendarEventBuilded = getDefaultCalendarEvent().build();
            const calendarEvents = [new CalendarEvent({ id: calendarEventBuilded._id.toString(), ...calendarEventBuilded })];

            await usecase.execute({ calendarEvents, channel: NotificationChannel.WEB });

            const createdNotifications = await notificationsRepository.find({ filter: {} });

            expect(createdNotifications).toHaveLength(2);
            expect(createdNotifications.map((n) => n.channel)).toIncludeAllMembers([NotificationChannel.WEB, NotificationChannel.WEB]);
            expect(createdNotifications.map((n) => n.type)).toIncludeAllMembers([
                NotificationType.POST_SUGGESTION,
                NotificationType.POST_SUGGESTION,
            ]);
        });
    });
});

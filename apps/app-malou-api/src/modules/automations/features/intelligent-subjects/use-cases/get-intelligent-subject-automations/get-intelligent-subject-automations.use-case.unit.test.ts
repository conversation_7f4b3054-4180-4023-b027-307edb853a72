import { container } from 'tsyringe';

import { IntelligentSubjectAutomationDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { IntelligentSubjectAutomationRelatedEntity, IntelligentSubjectName } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { GetIntelligentSubjectAutomationsUseCase } from ':modules/automations/features/intelligent-subjects/use-cases/get-intelligent-subject-automations/get-intelligent-subject-automations.use-case';
import { getDefaultIntelligentSubjectAutomation } from ':modules/automations/tests/intelligent-subject-automation.builder';
import { getDefaultUser } from ':modules/users/tests/user.builder';

describe('GetIntelligentSubjectAutomationsUseCase', () => {
    beforeAll(() => {
        registerRepositories(['IntelligentSubjectAutomationsRepository', 'UsersRepository']);
    });
    describe('execute', () => {
        const getIntelligentSubjectAutomationsUseCase = container.resolve(GetIntelligentSubjectAutomationsUseCase);
        it('should return restaurant intelligent subject automations', async () => {
            const restaurantId = newDbId();
            const restaurantId2 = newDbId();
            const hygieneRecipientEmails = ['<EMAIL>', '<EMAIL>'];
            const discriminationRecipientEmails = ['<EMAIL>', '<EMAIL>'];

            const testCase = new TestCaseBuilderV2<'users' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(restaurantId)
                                    .userId(dependencies.users()[0]._id)
                                    .relatedEntity(IntelligentSubjectAutomationRelatedEntity.REVIEWS)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .recipientEmails(hygieneRecipientEmails)
                                    .build(),
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(restaurantId)
                                    .userId(dependencies.users()[0]._id)
                                    .relatedEntity(IntelligentSubjectAutomationRelatedEntity.REVIEWS)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .recipientEmails(discriminationRecipientEmails)
                                    .build(),
                                // other restaurant automations
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(restaurantId2)
                                    .relatedEntity(IntelligentSubjectAutomationRelatedEntity.REVIEWS)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .recipientEmails([])
                                    .build(),
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(restaurantId2)
                                    .relatedEntity(IntelligentSubjectAutomationRelatedEntity.REVIEWS)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .recipientEmails([])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): IntelligentSubjectAutomationDto[] {
                    const documents = dependencies.intelligentSubjectAutomations;
                    return [
                        {
                            id: documents[0]._id.toString(),
                            restaurantId: documents[0].restaurantId.toString(),
                            feature: documents[0].feature,
                            active: documents[0].active,
                            relatedEntity: documents[0].relatedEntity,
                            action: documents[0].action,
                            subject: documents[0].subject,
                            recipientEmails: hygieneRecipientEmails,
                            createdAt: documents[0].createdAt.toISOString(),
                            updatedAt: documents[0].updatedAt.toISOString(),
                        },
                        {
                            id: documents[1]._id.toString(),
                            restaurantId: documents[1].restaurantId.toString(),
                            feature: documents[1].feature,
                            active: documents[1].active,
                            relatedEntity: documents[1].relatedEntity,
                            action: documents[1].action,
                            subject: documents[1].subject,
                            recipientEmails: discriminationRecipientEmails,
                            createdAt: documents[1].createdAt.toISOString(),
                            updatedAt: documents[1].updatedAt.toISOString(),
                        },
                    ];
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult() as IntelligentSubjectAutomationDto[];
            const result = await getIntelligentSubjectAutomationsUseCase.execute({
                restaurantId: restaurantId.toString(),
                relatedEntities: [IntelligentSubjectAutomationRelatedEntity.REVIEWS],
            });

            expect(result).toIncludeSameMembers(expectedResult);
        });
    });
});

import { singleton } from 'tsyringe';

import { DuplicateRestaurantIntelligentSubjectAutomationsBodyDto, IntelligentSubjectAutomationDto } from '@malou-io/package-dto';
import { isNotNil } from '@malou-io/package-utils';

import IntelligentSubjectAutomationsRepository from ':modules/automations/features/intelligent-subjects/repositories/intelligent-subjects.repository';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';

@singleton()
export class DuplicateIntelligentSubjectAutomationsUseCase {
    constructor(private readonly _intelligentSubjectAutomationsRepository: IntelligentSubjectAutomationsRepository) {}

    async execute({
        userId,
        fromRestaurantId,
        duplicationDetails,
    }: {
        userId: string;
        fromRestaurantId: string;
        duplicationDetails: DuplicateRestaurantIntelligentSubjectAutomationsBodyDto;
    }): Promise<IntelligentSubjectAutomationDto[]> {
        const { restaurantIds, relatedEntities } = duplicationDetails;

        const automationsToDuplicate = await this._intelligentSubjectAutomationsRepository.getIntelligentSubjectAutomations({
            restaurantId: fromRestaurantId,
            relatedEntities,
        });

        const promises = restaurantIds.map(async (restaurantId) => {
            const isReviewsIntelligentSubjectsEnabled = await this._isReviewsIntelligentSubjectsEnabledForRestaurant({ restaurantId });
            if (!isReviewsIntelligentSubjectsEnabled) {
                return null;
            }

            return this._intelligentSubjectAutomationsRepository.duplicateIntelligentSubjectAutomationsToRestaurant({
                userId,
                fromRestaurantId: fromRestaurantId,
                toRestaurantId: restaurantId,
                automations: automationsToDuplicate,
            });
        });

        const duplicatedAutomations = (await Promise.all(promises)).filter(isNotNil);

        return duplicatedAutomations.flat().map((automation) => automation.toDTO());
    }

    private readonly _isReviewsIntelligentSubjectsEnabledForRestaurant = async ({
        restaurantId,
    }: {
        restaurantId: string;
    }): Promise<boolean> => {
        const isFeatureEnabled = await isFeatureAvailableForRestaurant({
            restaurantId,
            featureName: 'release-reviews-intelligent-subjects',
        });
        return isFeatureEnabled;
    };
}

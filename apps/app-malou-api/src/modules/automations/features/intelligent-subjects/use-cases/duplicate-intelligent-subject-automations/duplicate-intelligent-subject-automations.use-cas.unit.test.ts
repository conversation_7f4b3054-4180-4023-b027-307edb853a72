import { container } from 'tsyringe';

import { IntelligentSubjectAutomationDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { IntelligentSubjectAutomationRelatedEntity, IntelligentSubjectName } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { DuplicateIntelligentSubjectAutomationsUseCase } from ':modules/automations/features/intelligent-subjects/use-cases/duplicate-intelligent-subject-automations/duplicate-intelligent-subject-automations.use-case';
import { getDefaultIntelligentSubjectAutomation } from ':modules/automations/tests/intelligent-subject-automation.builder';
import { getDefaultUser } from ':modules/users/tests/user.builder';

describe('DuplicateIntelligentSubjectAutomationsUseCase', () => {
    beforeAll(() => {
        registerRepositories(['IntelligentSubjectAutomationsRepository', 'UsersRepository']);
    });

    describe('execute', () => {
        const duplicateIntelligentSubjectAutomationsUseCase = container.resolve(DuplicateIntelligentSubjectAutomationsUseCase);

        it('should duplicate intelligent subject automations to multiple restaurants', async () => {
            const fromRestaurantId = newDbId();
            const toRestaurantId1 = newDbId();
            const toRestaurantId2 = newDbId();
            const hygieneRecipientEmails = ['<EMAIL>', '<EMAIL>'];

            const testCase = new TestCaseBuilderV2<'users' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(fromRestaurantId)
                                    .userId(dependencies.users()[0]._id)
                                    .relatedEntity(IntelligentSubjectAutomationRelatedEntity.REVIEWS)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .recipientEmails(hygieneRecipientEmails)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): IntelligentSubjectAutomationDto[] {
                    const automations = dependencies.intelligentSubjectAutomations;
                    return [
                        {
                            id: expect.any(String),
                            restaurantId: toRestaurantId1.toString(),
                            feature: automations[0].feature,
                            active: automations[0].active,
                            relatedEntity: automations[0].relatedEntity,
                            action: automations[0].action,
                            subject: automations[0].subject,
                            recipientEmails: hygieneRecipientEmails,
                            duplicatedFromRestaurantId: fromRestaurantId.toString(),
                            createdAt: expect.any(String),
                            updatedAt: expect.any(String),
                        },
                        {
                            id: expect.any(String),
                            restaurantId: toRestaurantId2.toString(),
                            feature: automations[0].feature,
                            active: automations[0].active,
                            relatedEntity: automations[0].relatedEntity,
                            action: automations[0].action,
                            subject: automations[0].subject,
                            recipientEmails: hygieneRecipientEmails,
                            duplicatedFromRestaurantId: fromRestaurantId.toString(),
                            createdAt: expect.any(String),
                            updatedAt: expect.any(String),
                        },
                    ];
                },
            });

            await testCase.build();

            jest.spyOn(
                duplicateIntelligentSubjectAutomationsUseCase as any,
                '_isReviewsIntelligentSubjectsEnabledForRestaurant'
            ).mockResolvedValue(true);

            const expectedResult = testCase.getExpectedResult() as IntelligentSubjectAutomationDto[];
            const seededObjects = testCase.getSeededObjects();
            const user = seededObjects.users[0];

            const result = await duplicateIntelligentSubjectAutomationsUseCase.execute({
                userId: user._id.toString(),
                fromRestaurantId: fromRestaurantId.toString(),
                duplicationDetails: {
                    restaurantIds: [toRestaurantId1.toString(), toRestaurantId2.toString()],
                    relatedEntities: [IntelligentSubjectAutomationRelatedEntity.REVIEWS],
                },
            });

            expect(result).toEqual(expectedResult);
        });

        // TODO: remove this test when the feature flag 'release-reviews-intelligent-subjects' is enabled by default
        it('should skip duplication for restaurants where the feature is disabled', async () => {
            const fromRestaurantId = newDbId();
            const toRestaurantId = newDbId();
            const userId = newDbId();

            const testCase = new TestCaseBuilderV2<'users' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(fromRestaurantId)
                                    .userId(dependencies.users()[0]._id)
                                    .relatedEntity(IntelligentSubjectAutomationRelatedEntity.REVIEWS)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .recipientEmails(['<EMAIL>'])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(): IntelligentSubjectAutomationDto[] {
                    return []; // No duplication expected
                },
            });

            await testCase.build();

            jest.spyOn(
                duplicateIntelligentSubjectAutomationsUseCase as any,
                '_isReviewsIntelligentSubjectsEnabledForRestaurant'
            ).mockResolvedValue(false);

            const result = await duplicateIntelligentSubjectAutomationsUseCase.execute({
                userId: userId.toString(),
                fromRestaurantId: fromRestaurantId.toString(),
                duplicationDetails: {
                    restaurantIds: [toRestaurantId.toString()],
                    relatedEntities: [IntelligentSubjectAutomationRelatedEntity.REVIEWS],
                },
            });

            expect(result).toEqual([]);
        });

        it('should handle empty automations gracefully', async () => {
            const fromRestaurantId = newDbId();
            const toRestaurantId = newDbId();
            const userId = newDbId();

            const testCase = new TestCaseBuilderV2<'intelligentSubjectAutomations'>({
                seeds: {
                    intelligentSubjectAutomations: {
                        data() {
                            return []; // No automations to duplicate
                        },
                    },
                },
                expectedResult(): IntelligentSubjectAutomationDto[] {
                    return []; // No duplication expected
                },
            });

            await testCase.build();

            jest.spyOn(
                duplicateIntelligentSubjectAutomationsUseCase as any,
                '_isReviewsIntelligentSubjectsEnabledForRestaurant'
            ).mockResolvedValue(true);

            const result = await duplicateIntelligentSubjectAutomationsUseCase.execute({
                userId: userId.toString(),
                fromRestaurantId: fromRestaurantId.toString(),
                duplicationDetails: {
                    restaurantIds: [toRestaurantId.toString()],
                    relatedEntities: [IntelligentSubjectAutomationRelatedEntity.REVIEWS],
                },
            });

            expect(result).toEqual([]);
        });

        it('should overwrite existing automations when duplicating', async () => {
            const fromRestaurantId = newDbId();
            const toRestaurantId = newDbId();
            const originalAutomationEmails = ['<EMAIL>', '<EMAIL>'];
            const existingRecipientEmails = ['<EMAIL>', '<EMAIL>'];

            const testCase = new TestCaseBuilderV2<'users' | 'intelligentSubjectAutomations'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                // original automation
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(fromRestaurantId)
                                    .userId(dependencies.users()[0]._id)
                                    .relatedEntity(IntelligentSubjectAutomationRelatedEntity.REVIEWS)
                                    .active(true)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .recipientEmails(originalAutomationEmails)
                                    .build(),

                                // automations to be replaced by duplication
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(toRestaurantId)
                                    .userId(dependencies.users()[0]._id)
                                    .relatedEntity(IntelligentSubjectAutomationRelatedEntity.REVIEWS)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .active(false)
                                    .recipientEmails(existingRecipientEmails)
                                    .build(),
                                // this one should not be replaced (not in the duplication)
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(toRestaurantId)
                                    .userId(dependencies.users()[0]._id)
                                    .relatedEntity(IntelligentSubjectAutomationRelatedEntity.REVIEWS)
                                    .subject(IntelligentSubjectName.DISCRIMINATION)
                                    .active(false)
                                    .recipientEmails(existingRecipientEmails)
                                    .build(),

                                // other restaurant automations
                                getDefaultIntelligentSubjectAutomation().restaurantId(newDbId()).build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): IntelligentSubjectAutomationDto[] {
                    const documents = dependencies.intelligentSubjectAutomations;
                    return [
                        {
                            id: documents[1]._id.toString(),
                            restaurantId: toRestaurantId.toString(),
                            feature: documents[1].feature,
                            active: documents[0].active,
                            relatedEntity: documents[1].relatedEntity,
                            action: documents[0].action,
                            subject: documents[0].subject,
                            recipientEmails: documents[0].recipientEmails,
                            duplicatedFromRestaurantId: fromRestaurantId.toString(),
                            createdAt: documents[1].createdAt.toISOString(),
                            updatedAt: expect.any(String),
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult() as IntelligentSubjectAutomationDto[];
            const seededObjects = testCase.getSeededObjects();
            const user = seededObjects.users[0];

            jest.spyOn(
                duplicateIntelligentSubjectAutomationsUseCase as any,
                '_isReviewsIntelligentSubjectsEnabledForRestaurant'
            ).mockResolvedValue(true);

            const result = await duplicateIntelligentSubjectAutomationsUseCase.execute({
                userId: user._id.toString(),
                fromRestaurantId: fromRestaurantId.toString(),
                duplicationDetails: {
                    restaurantIds: [toRestaurantId.toString()],
                    relatedEntities: [IntelligentSubjectAutomationRelatedEntity.REVIEWS],
                },
            });

            expect(result).toIncludeSameMembers(expectedResult);
        });
    });
});

import { singleton } from 'tsyringe';

import { UpsertRestaurantIntelligentSubjectAutomationsBodyDto } from '@malou-io/package-dto';
import {
    DbId,
    EntityRepository,
    IIntelligentSubjectAutomation,
    IntelligentSubjectAutomationModel,
    IUser,
    ReadPreferenceMode,
    toDbId,
} from '@malou-io/package-models';
import { AutomationFeature, IntelligentSubjectAutomationRelatedEntity, IntelligentSubjectName } from '@malou-io/package-utils';

import { IntelligentSubjectAutomation } from ':modules/automations/features/intelligent-subjects/entities/intelligent-subject-automation.entity';

type IIntelligentSubjectAutomationWithPartialUser = IIntelligentSubjectAutomation & { user: Pick<IUser, '_id' | 'defaultLanguage'> };
@singleton()
export default class IntelligentSubjectAutomationsRepository extends EntityRepository<IIntelligentSubjectAutomation> {
    constructor() {
        super(IntelligentSubjectAutomationModel);
    }

    async getIntelligentSubjectAutomations({
        restaurantId,
        relatedEntities,
    }: {
        restaurantId: string;
        relatedEntities: IntelligentSubjectAutomationRelatedEntity[];
    }): Promise<IntelligentSubjectAutomation[]> {
        const docs = await this.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                feature: AutomationFeature.INTELLIGENT_SUBJECT,
                relatedEntity: { $in: relatedEntities },
            },
            options: {
                populate: [
                    {
                        path: 'user',
                        select: { _id: 1, defaultLanguage: 1 },
                    },
                ],
                lean: true,
                readPreference: ReadPreferenceMode.SECONDARY,
            },
        });

        return docs.map((doc) => this.toEntity(doc));
    }

    async getIntelligentSubjectAutomationsBySubjects({
        restaurantId,
        relatedEntities,
        subjects,
    }: {
        restaurantId: string;
        relatedEntities: IntelligentSubjectAutomationRelatedEntity[];
        subjects: IntelligentSubjectName[];
    }): Promise<IntelligentSubjectAutomation[]> {
        const docs = await this.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                feature: AutomationFeature.INTELLIGENT_SUBJECT,
                relatedEntity: { $in: relatedEntities },
                subject: { $in: subjects },
            },
            options: {
                populate: [
                    {
                        path: 'user',
                        select: { _id: 1, defaultLanguage: 1 },
                    },
                ],
                lean: true,
                readPreference: ReadPreferenceMode.SECONDARY,
            },
        });

        return docs.map((doc) => this.toEntity(doc));
    }

    // this method ensures that the automations are unique by restaurantId_relatedEntity_subject
    // if there are two automations with the same restaurantId, relatedEntity and subject in the automations array,
    // only the last will be kept !
    async upsertIntelligentSubjectAutomations({
        userId,
        restaurantId,
        automations,
    }: {
        userId: string;
        restaurantId: string;
        automations: UpsertRestaurantIntelligentSubjectAutomationsBodyDto['automations'];
    }): Promise<IntelligentSubjectAutomation[]> {
        const upsertedIds: DbId[] = [];
        for (const automation of automations) {
            const filter: Partial<IIntelligentSubjectAutomation> = {
                restaurantId: toDbId(restaurantId),
                feature: AutomationFeature.INTELLIGENT_SUBJECT,
                relatedEntity: automation.relatedEntity,
                subject: automation.subject,
            };

            const existingDoc = await this.findOne({ filter, options: { lean: true } });
            if (existingDoc) {
                // Update the existing document
                const activeFlagHasChanged = automation.active !== existingDoc.active;
                const lastActiveUpdatedAt = activeFlagHasChanged ? new Date() : existingDoc.lastActiveUpdatedAt;
                const updatedDoc = await this.findOneAndUpdate({
                    filter,
                    update: {
                        ...automation,
                        lastActiveUpdatedAt,
                        restaurantId: toDbId(restaurantId),
                    },
                    options: { lean: true, new: true },
                    projection: { _id: 1 },
                });
                if (updatedDoc) {
                    upsertedIds.push(updatedDoc._id);
                }
            } else {
                // If no existing document, create a new one
                const newDoc = await this.create({
                    data: {
                        userId: toDbId(userId),
                        restaurantId: toDbId(restaurantId),
                        feature: AutomationFeature.INTELLIGENT_SUBJECT,
                        lastActiveUpdatedAt: new Date(),
                        ...automation,
                        duplicatedFromRestaurantId: automation.duplicatedFromRestaurantId
                            ? toDbId(automation.duplicatedFromRestaurantId)
                            : undefined,
                    },
                    options: { lean: true },
                });

                upsertedIds.push(newDoc._id);
            }
        }

        // Fetch all upserted documents
        const upsertedDocs = await this.find({
            filter: { _id: { $in: upsertedIds } },
            options: {
                populate: [
                    {
                        path: 'user',
                        select: { _id: 1, defaultLanguage: 1 },
                    },
                ],
                readPreference: ReadPreferenceMode.SECONDARY,
                lean: true,
            },
        });

        return upsertedDocs.map((doc) => this.toEntity(doc));
    }

    async duplicateIntelligentSubjectAutomationsToRestaurant({
        userId,
        fromRestaurantId,
        toRestaurantId,
        automations,
    }: {
        userId: string;
        fromRestaurantId: string;
        toRestaurantId: string;
        automations: IntelligentSubjectAutomation[];
    }): Promise<IntelligentSubjectAutomation[]> {
        const automationsToDuplicate = automations.map((automation) => ({
            restaurantId: toRestaurantId,
            feature: AutomationFeature.INTELLIGENT_SUBJECT,
            relatedEntity: automation.relatedEntity,
            subject: automation.subject,
            action: automation.action,
            recipientEmails: automation.recipientEmails,
            active: automation.active,
            lastActiveUpdatedAt: automation.lastActiveUpdatedAt,
            duplicatedFromRestaurantId: fromRestaurantId,
        }));

        return this.upsertIntelligentSubjectAutomations({
            userId,
            restaurantId: toRestaurantId,
            automations: automationsToDuplicate,
        });
    }

    toEntity(entity: IIntelligentSubjectAutomationWithPartialUser): IntelligentSubjectAutomation {
        return new IntelligentSubjectAutomation({
            id: entity._id.toString(),
            restaurantId: entity.restaurantId.toString(),
            user: {
                id: entity.user._id.toString(),
                defaultLanguage: entity.user.defaultLanguage,
            },
            feature: AutomationFeature.INTELLIGENT_SUBJECT,
            active: entity.active,
            relatedEntity: entity.relatedEntity,
            action: entity.action,
            subject: entity.subject,
            recipientEmails: entity.recipientEmails,
            ...(entity.duplicatedFromRestaurantId && {
                duplicatedFromRestaurantId: entity.duplicatedFromRestaurantId.toString(),
            }),
            lastActiveUpdatedAt: entity.lastActiveUpdatedAt,
            createdAt: entity.createdAt,
            updatedAt: entity.updatedAt,
        });
    }
}

import { Builder } from 'builder-pattern';

import { IRestaurant, newDbId } from '@malou-io/package-models';
import { BusinessCategory, CountryCode, GMapsApiVersion, RestaurantCalendarEventsCountry } from '@malou-io/package-utils';

type RestaurantPayload = IRestaurant;

const _buildRestaurant = (restaurant: RestaurantPayload) => Builder<RestaurantPayload>(restaurant);

export const getDefaultRestaurant = () =>
    _buildRestaurant({
        _id: newDbId(),
        name: 'Test Restaurant',
        internalName: 'Test Internal Name ' + newDbId().toString(),
        address: {
            administrativeArea: 'Test Administrative Area',
            country: 'France',
            formattedAddress: 'Test Formatted Address',
            locality: 'Test Locality',
            postalCode: '75000',
            regionCode: CountryCode.FRANCE,
            route: 'Test Route',
            streetNumber: 'Test Street Number',
        },
        phone: {
            digits: 65432123,
            prefix: 33,
        },
        ai: {
            monthlyCallCount: 0,
        },
        website: 'https://malou.io',
        logo: newDbId(),
        cover: newDbId(),
        active: true,
        access: [],
        bookmarkedPosts: [],
        bricks: [],
        calendarEvents: [],
        categoryList: [],
        coverChanged: false,
        createdAt: new Date(),
        descriptions: [],
        isClosedTemporarily: false,
        logoChanged: false,
        placeId: 'Test Place Id',
        relatedUrls: [],
        specialHours: [],
        regularHours: [],
        type: BusinessCategory.LOCAL_BUSINESS,
        uniqueKey: `facebook_${newDbId().toString()}`,
        updatedAt: new Date(),
        openingDate: new Date(),
        organizationId: newDbId(),
        calendarEventsCountry: RestaurantCalendarEventsCountry.FRANCE,
        boosterPack: {
            _id: newDbId(),
            activated: true,
            activationDate: new Date(),
        },
        gMapsApiVersion: GMapsApiVersion.V2,
    });

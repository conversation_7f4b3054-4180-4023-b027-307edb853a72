import { container } from 'tsyringe';

import { CountryCode } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultOrganization } from ':modules/organizations/organization.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { SearchRestaurantsV2UseCase } from ':modules/restaurants/use-cases/search-restaurants-v2/search-restaurants-v2.use-case';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';

describe('SearchRestaurantsV2UseCase', () => {
    let testCase: TestCaseBuilderV2<'restaurants' | 'organizations' | 'users' | 'userRestaurants'>;
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'OrganizationsRepository', 'UsersRepository', 'UserRestaurantsRepository']);

        testCase = new TestCaseBuilderV2<'restaurants' | 'organizations' | 'users' | 'userRestaurants'>({
            seeds: {
                organizations: {
                    data: () => [getDefaultOrganization().name('Malou Org').build()],
                },
                restaurants: {
                    data: (deps) => [
                        getDefaultRestaurant()
                            .organizationId(deps.organizations()[0]._id)
                            .name('Le Gourmet')
                            .internalName('Le Gourmet Internal')
                            .address({
                                administrativeArea: 'Area',
                                country: 'France',
                                formattedAddress: '123 Rue de Paris',
                                locality: 'Paris',
                                postalCode: '75000',
                                regionCode: CountryCode.FRANCE,
                                route: 'Rue de Paris',
                                streetNumber: '123',
                            })
                            .active(true)
                            .createdAt(new Date('2023-01-01'))
                            .build(),
                        getDefaultRestaurant()
                            .organizationId(deps.organizations()[0]._id)
                            .name('Le Grand Gourmet')
                            .internalName('le Grand Gourmet Internal')
                            .address({
                                administrativeArea: 'Area',
                                country: 'France',
                                formattedAddress: '123 Rue de Paris',
                                locality: 'Paris',
                                postalCode: '75000',
                                regionCode: CountryCode.FRANCE,
                                route: 'Rue de Paris',
                                streetNumber: '123',
                            })
                            .createdAt(new Date('2024-01-01'))
                            .active(true)
                            .build(),
                        getDefaultRestaurant()
                            .organizationId(deps.organizations()[0]._id)
                            .name('Grand Gourmet')
                            .internalName('Grand Gourmet Internal')
                            .address({
                                administrativeArea: 'Area',
                                country: 'France',
                                formattedAddress: '123 Rue de Paris',
                                locality: 'Paris',
                                postalCode: '75000',
                                regionCode: CountryCode.FRANCE,
                                route: 'Rue de Paris',
                                streetNumber: '123',
                            })
                            .active(true)
                            .createdAt(new Date('2022-01-01'))
                            .build(),
                        getDefaultRestaurant() // NON ACTIVE
                            .organizationId(deps.organizations()[0]._id)
                            .name('Gourmet')
                            .internalName('Gourmet Internal')
                            .address({
                                administrativeArea: 'Area',
                                country: 'France',
                                formattedAddress: '123 Rue de Paris',
                                locality: 'Paris',
                                postalCode: '75000',
                                regionCode: CountryCode.FRANCE,
                                route: 'Rue de Paris',
                                streetNumber: '123',
                            })
                            .active(false)
                            .build(),
                    ],
                },
                users: {
                    data: () => [
                        getDefaultUser().name('Alice').lastname('Doe').email('<EMAIL>').build(),
                        getDefaultUser().name('Bob').lastname('Smith').email('<EMAIL>').build(),
                    ],
                },
                userRestaurants: {
                    data: (deps) => [
                        getDefaultUserRestaurant().userId(deps.users()[0]._id).restaurantId(deps.restaurants()[0]._id).build(),
                        getDefaultUserRestaurant().userId(deps.users()[0]._id).restaurantId(deps.restaurants()[1]._id).build(),
                        getDefaultUserRestaurant().userId(deps.users()[1]._id).restaurantId(deps.restaurants()[1]._id).build(),
                    ],
                },
            },
            expectedResult: (deps) => ({
                result1: {
                    data: [deps.restaurants[1]._id.toString(), deps.restaurants[0]._id.toString()],
                    total: 3,
                },
                result2: {
                    data: [deps.restaurants[1]._id.toString(), deps.restaurants[0]._id.toString(), deps.restaurants[2]._id.toString()],
                    total: 3,
                },
            }),
        });
    });

    it('should return restaurants with organization and managers', async () => {
        await testCase.build();
        const searchRestaurantsV2UseCase = container.resolve(SearchRestaurantsV2UseCase);

        const expectedResult = testCase.getExpectedResult();

        const result1 = await searchRestaurantsV2UseCase.execute({ text: '', limit: 2, offset: 0, active: true });

        const expectedResult1 = expectedResult.result1;
        expect(result1.total).toEqual(expectedResult1.total);
        expect(result1.data.map((restaurant) => restaurant._id?.toString())).toEqual(expect.arrayContaining(expectedResult.result1.data));

        const result2 = await searchRestaurantsV2UseCase.execute({ text: 'Gourmet', limit: 10, offset: 0, active: true });

        const expectedResult2 = expectedResult.result2;
        expect(result2.total).toEqual(expectedResult2.total);
        expect(result2.data.map((restaurant) => restaurant._id?.toString())).toEqual(expect.arrayContaining(expectedResult.result2.data));
    });
});

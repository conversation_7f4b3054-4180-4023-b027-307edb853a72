import { singleton } from 'tsyringe';

import { RestaurantCalendarEventsCountryType } from '@malou-io/package-utils';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class UpdateRestaurantCalendarEventsCountryUseCase {
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute(restaurantId: string, country: RestaurantCalendarEventsCountryType): Promise<void> {
        await this._restaurantsRepository.updateRestaurantCalendarEventsCountry(restaurantId, country);
    }
}

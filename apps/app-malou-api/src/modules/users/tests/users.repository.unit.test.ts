import { UserModel } from '@malou-io/package-models';

import { registerRepositories } from ':helpers/tests/testing-utils';
import { getDefaultUser } from ':modules/users/tests/user.builder';
import { UsersRepository } from ':modules/users/users.repository';

describe('UsersRepository.filterNonVerifiedKnownEmails', () => {
    let usersRepository: UsersRepository;

    beforeAll(async () => {
        await registerRepositories(['UsersRepository']);
        usersRepository = new UsersRepository();
    });

    afterEach(async () => {
        await UserModel.deleteMany({});
    });

    it('should return all emails if none are known', async () => {
        const emails = ['<EMAIL>', '<EMAIL>'];
        const result = await usersRepository.filterNonVerifiedKnownEmails(emails);
        expect(result).toEqual(emails);
    });

    it('should return only verified and not deactivated emails among known users', async () => {
        const knownVerified = getDefaultUser().email('<EMAIL>').verified(true).hasBeenDeactivatedByAdmin(false).build();
        const knownUnverified = getDefaultUser().email('<EMAIL>').verified(false).hasBeenDeactivatedByAdmin(false).build();
        const knownDeactivated = getDefaultUser().email('<EMAIL>').verified(true).hasBeenDeactivatedByAdmin(true).build();
        await UserModel.create([knownVerified, knownUnverified, knownDeactivated]);
        const emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
        const result = await usersRepository.filterNonVerifiedKnownEmails(emails);
        expect(result).toEqual(['<EMAIL>', '<EMAIL>']);
    });

    it('should return empty array if all known users are unverified or deactivated', async () => {
        const knownUnverified = getDefaultUser().email('<EMAIL>').verified(false).hasBeenDeactivatedByAdmin(false).build();
        const knownDeactivated = getDefaultUser().email('<EMAIL>').verified(true).hasBeenDeactivatedByAdmin(true).build();
        await UserModel.create([knownUnverified, knownDeactivated]);
        const emails = ['<EMAIL>', '<EMAIL>'];
        const result = await usersRepository.filterNonVerifiedKnownEmails(emails);
        expect(result).toEqual([]);
    });

    it('should handle a mix of known and unknown emails', async () => {
        const knownVerified = getDefaultUser().email('<EMAIL>').verified(true).hasBeenDeactivatedByAdmin(false).build();
        const knownDeactivated = getDefaultUser().email('<EMAIL>').verified(true).hasBeenDeactivatedByAdmin(true).build();
        await UserModel.create([knownVerified, knownDeactivated]);
        const emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
        const result = await usersRepository.filterNonVerifiedKnownEmails(emails);
        expect(result).toEqual(['<EMAIL>', '<EMAIL>']);
    });
});

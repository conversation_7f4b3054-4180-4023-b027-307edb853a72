import { render } from '@react-email/render';
import assert from 'assert';
import { isNil } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { SimpleRestaurant } from '@malou-io/package-dto';
import { WeeklyReviewsReports } from '@malou-io/package-emails';
import { DbId, ID, IReviewWithSemanticAnalysisAndTranslations, toDbId } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    APP_DEFAULT_LANGUAGE,
    LANGUAGES,
    MalouErrorCode,
    MIN_POSITIVE_REVIEW_RATING,
    PlatformKey,
    processPromisesByChunks,
    ReportType,
    TimeInMilliseconds,
    waitFor,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { filterByRequiredKeys } from ':helpers/validators/filter-by-required-keys';
import { ApiKeysRepository } from ':modules/api-keys/api-keys.repository';
import { PlatformsUseCases } from ':modules/platforms/platforms.use-cases';
import { Report, ReportMetaData } from ':modules/reports/report.entity';
import { ReportSendParamsValidatorDto } from ':modules/reports/reports.dto';
import ReportsRepository from ':modules/reports/reports.repository';
import { DeleteReportPDFService } from ':modules/reports/services/delete-report-pdf.service';
import { GenerateReportPDFService } from ':modules/reports/services/generate-report-pdf.service';
import { SendReportEmailService } from ':modules/reports/services/send-report-email.service';
import { TranslateReviewTextService } from ':modules/reports/services/translate-review-text.service';
import { ReviewsMetadata } from ':modules/reports/use-cases/review-reports/review-reports.types';
import {
    PlatformReviewsReportData,
    RestaurantWeeklyReviewsReportData,
    WeeklyReviewsReport,
    WeeklyReviewsReportData,
} from ':modules/reports/use-cases/review-reports/weekly-reports/reviews-report-weekly.entity';
import { WeeklyReviewReportsMapper } from ':modules/reports/use-cases/review-reports/weekly-reports/weekly-reports.mapper';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { UsersRepository } from ':modules/users/users.repository';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';
import { ReviewsSemanticAnalysisService } from ':services/semantic-analysis/reviews/reviews.semantic-analysis.service';
import { ReviewSemanticAnalysis } from ':services/semantic-analysis/reviews/reviews.semantic-analysis.types';

interface WeeklyReviewsRawAggregationResult {
    data: {
        address: string;
        name: string;
        logo: string;
        restaurantId: DbId;
        metadata: ReviewsMetadata;
        previousMetadata: ReviewsMetadata;
        reviews: {
            currentCount: number;
            previousCount: number;
            currentRatingAvg: number;
            previousRatingAvg: number;
            platformKey: PlatformKey;
            currentReviews: IReviewWithSemanticAnalysisAndTranslations[];
        }[];
    }[];
    metadata: ReviewsMetadata;
    previousMetadata: ReviewsMetadata;
}

interface PlatformRating {
    restaurantId: ID;
    platformKey: PlatformKey;
    rating: number;
}

@singleton()
export class WeeklyReviewsSendReportUseCase {
    private _apiKey!: string;
    private readonly reportType = ReportType.WEEKLY_REVIEWS;
    private readonly SEND_REPORT_EMAILS_CHUNK_SIZE = 5;
    private readonly INTERVAL_BETWEEN_EMAILS = 5;
    private readonly TOP_REVIEWS_MIN_RATING = MIN_POSITIVE_REVIEW_RATING;
    private readonly FLOP_REVIEWS_MAX_RATING = MIN_POSITIVE_REVIEW_RATING;

    constructor(
        private readonly _reportsRepository: ReportsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _platformsUseCases: PlatformsUseCases,
        private readonly _weeklyReviewReportsMapper: WeeklyReviewReportsMapper,
        private readonly _reviewsSemanticAnalysisService: ReviewsSemanticAnalysisService,
        private readonly _apiKeysRepository: ApiKeysRepository,
        private readonly _sendReportEmailService: SendReportEmailService,
        private readonly _generateReportPDFService: GenerateReportPDFService,
        private readonly _deleteReportPDFService: DeleteReportPDFService,
        private readonly _translateReviewTextService: TranslateReviewTextService,
        private readonly _usersRepository: UsersRepository
    ) {}

    async execute({ reportId, configurationId, options }: ReportSendParamsValidatorDto): Promise<void> {
        const [key, report] = await Promise.all([
            this._apiKeysRepository.findOne({
                filter: { name: 'email' },
                options: { lean: true },
            }),
            this._reportsRepository.getReportById({ reportId }),
        ]);

        assert(
            report,
            new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Report not found',
                metadata: { reportId },
            })
        );
        assert(key, '[DailyReviewsSendReportUseCase] API key not found');
        this._apiKey = key.apiKey;

        const configuration = report?.configurations?.find((config) => config.id === configurationId);

        if (isNil(report) || isNil(configuration)) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Report not found',
                metadata: {
                    reportId,
                },
            });
        }

        // For debug, send to custom emails
        if (options?.sendTo) {
            configuration.recipients = options.sendTo;
        }

        const reportData = await this._getWeeklyReportData(report, configuration);
        assert(reportData, '[WeeklyReviewsSendReportUseCase] Report data not found');
        const sendReports = await this._sendReports(reportData, options?.shouldPreventEmailSending);

        await processPromisesByChunks(sendReports, this.SEND_REPORT_EMAILS_CHUNK_SIZE, async (_) => {
            await waitFor(this.INTERVAL_BETWEEN_EMAILS * TimeInMilliseconds.SECOND);
        });

        await this._deleteReportPdf(reportData);
    }

    private async _getWeeklyReportData(report: Report, config: Report['configurations'][0]): Promise<WeeklyReviewsReport | null> {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            logger.info(`${report.getLogGroup()} Get report data`, metaData);

            const weeklyReviewsReportData = await this._getReportData(config.restaurants, metaData);

            assert(report.user, '[WeeklyReviewsSendReportUseCase] Report user not found');
            const emailProps = await this._weeklyReviewReportsMapper.mapToEmailProps(
                report.user,
                weeklyReviewsReportData,
                config.restaurants
            );

            const weeklyReviewsReport = new WeeklyReviewsReport(emailProps, report, config);

            const pdfFileUrl = await this._generateReportPDFService.execute({
                report,
                configuration: config,
                pdfFolderName: weeklyReviewsReport.getPdfFolderName(),
                pdfFileName: weeklyReviewsReport.getPdfFileName(),
                html: render(WeeklyReviewsReports(emailProps)),
            });
            if (pdfFileUrl) {
                weeklyReviewsReport.setPdfFileUrl(pdfFileUrl);
            }

            return weeklyReviewsReport;
        } catch (err) {
            logger.error(`${report.getLogGroup()} Get report data`, {
                err,
                ...metaData,
            });
            return null;
        }
    }

    private async _getReportData(restaurants: SimpleRestaurant[], metaData: ReportMetaData): Promise<WeeklyReviewsReportData> {
        const [rawAggregationResult, { currentPlatformRatings, previousPlatformRatings }] = await Promise.all([
            this._reviewsRepository.getReviewsForReports<WeeklyReviewsRawAggregationResult>(restaurants, this.reportType),
            this._getPlatformRatings(restaurants),
        ]);

        const [data, reportGlobalReviewsSemanticAnalysisOverview] = await Promise.all([
            this._getRestaurantsData({
                restaurantResults: rawAggregationResult.data,
                metaData,
                currentPlatformRatings,
                previousPlatformRatings,
            }),
            this._getSemanticAnalysisFromAI(rawAggregationResult.data, metaData),
        ]);

        // Translating reviews
        if (data.length) {
            for (const restaurantData of data) {
                if (restaurantData.platforms.length) {
                    const platformsData = restaurantData.platforms;
                    if (platformsData.length) {
                        for (const review of platformsData) {
                            if (review.topReviews.length) {
                                review.topReviews = await this._translateReviews(review.topReviews, metaData);
                            }
                            if (review.flopReviews.length) {
                                review.flopReviews = await this._translateReviews(review.flopReviews, metaData);
                            }
                        }
                    }
                }
            }
        }

        return {
            data,
            metadata: rawAggregationResult.metadata,
            previousMetadata: rawAggregationResult.previousMetadata,
            reportGlobalReviewsSemanticAnalysisOverview,
        };
    }

    private async _getPlatformRatings(restaurants: SimpleRestaurant[]) {
        const [currentPlatformRatings, previousPlatformRatings] = await Promise.all([
            this._platformsUseCases.getLatestPlatformRatings({
                restaurantIds: restaurants.map((r) => toDbId(r.id)),
            }),
            this._platformsUseCases.getLatestPlatformRatings({
                restaurantIds: restaurants.map((r) => toDbId(r.id)),
                beforeDate: DateTime.now().minus({ week: 1 }).toJSDate(),
            }),
        ]);

        return {
            currentPlatformRatings,
            previousPlatformRatings,
        };
    }

    private async _getRestaurantsData({
        restaurantResults,
        metaData,
        currentPlatformRatings,
        previousPlatformRatings,
    }: {
        restaurantResults: WeeklyReviewsRawAggregationResult['data'];
        metaData: ReportMetaData;
        currentPlatformRatings: PlatformRating[];
        previousPlatformRatings: PlatformRating[];
    }): Promise<RestaurantWeeklyReviewsReportData[]> {
        return Promise.all(
            restaurantResults.map(async (restaurantResult) =>
                this._getRestaurantData({ restaurantResult, metaData, currentPlatformRatings, previousPlatformRatings })
            )
        ).then((results) => results.filter((result) => result !== null));
    }

    private async _getRestaurantData({
        restaurantResult,
        metaData,
        currentPlatformRatings,
        previousPlatformRatings,
    }: {
        restaurantResult: WeeklyReviewsRawAggregationResult['data'][0];
        metaData: ReportMetaData;
        currentPlatformRatings: PlatformRating[];
        previousPlatformRatings: PlatformRating[];
    }): Promise<RestaurantWeeklyReviewsReportData | null> {
        try {
            const reviewsSemanticAnalysisOverview = await this._getSemanticAnalysisFromAI([restaurantResult], metaData);

            const platformReviewsData: PlatformReviewsReportData[] = this._getDataByPlatform({
                restaurantResult,
                currentPlatformRatings,
                previousPlatformRatings,
            });

            return {
                restaurantId: restaurantResult.restaurantId,
                metadata: restaurantResult.metadata,
                previousMetadata: restaurantResult.previousMetadata,
                address: restaurantResult.address,
                image: restaurantResult.logo,
                name: restaurantResult.name,
                platforms: platformReviewsData,
                ...(reviewsSemanticAnalysisOverview && {
                    reviewsSemanticAnalysisOverview,
                }),
            };
        } catch (err) {
            logger.error(`[Weekly reviews report] Failed to get data for restaurant`, {
                err,
                ...metaData,
                restaurantId: restaurantResult.restaurantId.toString(),
            });

            return null;
        }
    }

    private _getDataByPlatform({
        restaurantResult,
        currentPlatformRatings,
        previousPlatformRatings,
    }: {
        restaurantResult: WeeklyReviewsRawAggregationResult['data'][0];
        currentPlatformRatings: PlatformRating[];
        previousPlatformRatings: PlatformRating[];
    }): PlatformReviewsReportData[] {
        return restaurantResult.reviews.map((platformReviews) => {
            const topReviews = filterByRequiredKeys(platformReviews.currentReviews, ['rating', 'text'])
                .filter((review) => review.rating > this.TOP_REVIEWS_MIN_RATING && review.text?.length > 0)
                .sort((a, b) => b.rating - a.rating)
                .slice(0, 3);
            const flopReviews = filterByRequiredKeys(platformReviews.currentReviews, ['rating', 'text'])
                .filter((review) => review.rating <= this.FLOP_REVIEWS_MAX_RATING && review.text?.length > 0)
                .sort((a, b) => a.rating - b.rating)
                .slice(0, 3);
            const platformRating = currentPlatformRatings.find(
                ({ restaurantId, platformKey }) =>
                    restaurantId.toString() === restaurantResult.restaurantId.toString() && platformKey === platformReviews.platformKey
            )?.rating;
            const previousPlatformRating = previousPlatformRatings.find(
                ({ restaurantId, platformKey }) =>
                    restaurantId.toString() === restaurantResult.restaurantId.toString() && platformKey === platformReviews.platformKey
            )?.rating;

            return {
                platformKey: platformReviews.platformKey,
                topReviews,
                flopReviews,
                metadata: {
                    count: platformReviews.currentCount,
                    platformRating: platformRating ?? 0,
                    ratingAvg: platformReviews.currentRatingAvg,
                },
                previousMetadata: {
                    count: platformReviews.previousCount,
                    platformRating: previousPlatformRating ?? 0,
                    ratingAvg: platformReviews.previousRatingAvg,
                },
            };
        });
    }

    private async _getSemanticAnalysisFromAI(restaurantResults: WeeklyReviewsRawAggregationResult['data'], metaData: ReportMetaData) {
        try {
            const restaurantsData = await Promise.all(
                restaurantResults.map(async (restaurantResult) => {
                    const reviews = restaurantResult.reviews.flatMap((platformReviews) => platformReviews.currentReviews);
                    const reviewsSemanticAnalysis = await this._getReviewsSemanticAnalysis(
                        restaurantResult.restaurantId.toString(),
                        reviews
                    );
                    return {
                        restaurantName: restaurantResult.name,
                        reviewsSemanticAnalysis,
                    };
                })
            );

            const { semanticAnalysisResult: aiSemanticAnalysis } = await this._reviewsSemanticAnalysisService.getSemanticAnalysisOverview({
                collection: AiInteractionRelatedEntityCollection.REPORTS,
                collectionId: metaData.reportId,
                language: metaData.user?.defaultLanguage ? LANGUAGES[metaData.user.defaultLanguage] : LANGUAGES[APP_DEFAULT_LANGUAGE],
                restaurantsData,
            });

            if (!aiSemanticAnalysis || aiSemanticAnalysis === '') {
                logger.warn('[Weekly reviews report] Semantic analysis from AI returned empty value', metaData);

                return undefined;
            }

            return aiSemanticAnalysis;
        } catch (err) {
            logger.error('[Weekly reviews report] Semantic analysis from AI', {
                err,
                ...metaData,
            });

            return undefined;
        }
    }

    private async _getReviewsSemanticAnalysis(
        restaurantId: string,
        reviews: IReviewWithSemanticAnalysisAndTranslations[]
    ): Promise<ReviewSemanticAnalysis[]> {
        const isSemanticAnalysisFeatureEnabled = await isFeatureAvailableForRestaurant({
            featureName: 'release-new-semantic-analysis',
            restaurantId,
        });

        if (isSemanticAnalysisFeatureEnabled) {
            return reviews.flatMap(
                (review) =>
                    review.semanticAnalysisSegments?.map(({ category, sentiment, segment }) => ({
                        category,
                        sentiment,
                        segment,
                    })) ?? []
            );
        }
        return reviews.flatMap(
            (review) =>
                review.semanticAnalysis?.segmentAnalyses?.map(({ tag, sentiment, originalSegment }) => ({
                    category: tag,
                    sentiment,
                    segment: originalSegment,
                })) ?? []
        );
    }

    private async _sendReports(
        weeklyReviewsReport: WeeklyReviewsReport,
        shouldPreventEmailSending?: boolean
    ): Promise<(() => Promise<void>)[]> {
        const metaData = weeklyReviewsReport.getMetaData();
        const logGroup = weeklyReviewsReport.getLogGroup();

        const validRecipients = await this._usersRepository.filterNonVerifiedKnownEmails(weeklyReviewsReport.getRecipients());

        logger.info(`${logGroup} Preparing to send reports`, {
            ...metaData,
            recipientCount: weeklyReviewsReport.getRecipients().length,
            validRecipientCount: validRecipients.length,
        });

        const emailsPromises: (() => Promise<void>)[] = [];
        for (const recipient of validRecipients) {
            emailsPromises.push(() => this._sendReport(weeklyReviewsReport, recipient, shouldPreventEmailSending));
        }

        return emailsPromises;
    }

    private async _sendReport(weeklyReviewsReport: WeeklyReviewsReport, recipient: string, shouldPreventEmailSending?: boolean) {
        const metaData = {
            recipient,
            ...weeklyReviewsReport.getMetaData(),
        };
        const logGroup = weeklyReviewsReport.getLogGroup();

        try {
            logger.info(`${logGroup} Preparing to send report`, metaData);

            const unsubscribeLink = await weeklyReviewsReport.buildUnsubscribeLink(recipient);
            const trackingUrl = weeklyReviewsReport.buildTrackingUrl({ email: recipient, apiKey: this._apiKey });

            const html = render(
                WeeklyReviewsReports({
                    ...weeklyReviewsReport.getEmailProps(),
                    unsubscribeLink,
                    trackingUrl,
                })
            );

            await this._sendReportEmailService.execute({
                report: weeklyReviewsReport.getReport(),
                recipient,
                configuration: weeklyReviewsReport.getConfiguration(),
                reportEmailSubject: weeklyReviewsReport.getReportEmailSubject(),
                language: weeklyReviewsReport.getEmailProps().locale,
                pdfFileUrl: weeklyReviewsReport.getPdfFileUrl(),
                html,
                shouldPreventEmailSending,
            });
        } catch (err) {
            logger.error(`${logGroup} Failed to send report`, {
                err,
                ...metaData,
            });
        }
    }

    async _deleteReportPdf(reportData: WeeklyReviewsReport): Promise<void> {
        const pdfFileUrl = reportData.getPdfFileUrl();

        if (isNil(pdfFileUrl)) {
            logger.warn(`${reportData.getLogGroup()} PDF file not found`, reportData.getMetaData());
            return;
        }

        await this._deleteReportPDFService.execute({
            report: reportData.getReport(),
            configuration: reportData.getConfiguration(),
            pdfFolderName: reportData.getPdfFolderName(),
            pdfFileName: reportData.getPdfFileName(),
            pdfFileUrl,
        });
    }

    private async _translateReviews(
        reviews: IReviewWithSemanticAnalysisAndTranslations[],
        metaData: ReportMetaData
    ): Promise<IReviewWithSemanticAnalysisAndTranslations[]> {
        const translatedReviews = reviews;
        if (translatedReviews.length) {
            for (const review of translatedReviews) {
                if (review.text && metaData.user && review.lang !== metaData.user.defaultLanguage) {
                    try {
                        const translatedReview = await this._translateReviewTextService.execute(review, {
                            _id: metaData.user.id,
                            defaultLanguage: metaData.user.defaultLanguage,
                        });
                        if (translatedReview) {
                            review.text = translatedReview.text;
                        }
                    } catch (error: any) {
                        logger.error('[Weekly reviews report] Cannot translate review text', {
                            error: error.stack,
                            reviewId: review._id,
                            userId: metaData.user.id,
                        });
                    }
                }
            }
        }
        return translatedReviews;
    }
}

import { render } from '@react-email/render';
import assert from 'assert';
import { isNil } from 'lodash';
import { singleton } from 'tsyringe';

import { DailyReviewsReports } from '@malou-io/package-emails';
import { IReviewWithSemanticAnalysisAndTranslations } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    APP_DEFAULT_LANGUAGE,
    LANGUAGES,
    MalouErrorCode,
    processPromisesByChunks,
    ReportType,
    TimeInMilliseconds,
    waitFor,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { ApiKeysRepository } from ':modules/api-keys/api-keys.repository';
import { Report, ReportMetaData } from ':modules/reports/report.entity';
import { ReportSendParamsValidatorDto } from ':modules/reports/reports.dto';
import ReportsRepository from ':modules/reports/reports.repository';
import { DeleteReportPDFService } from ':modules/reports/services/delete-report-pdf.service';
import { GenerateReportPDFService } from ':modules/reports/services/generate-report-pdf.service';
import { SendReportEmailService } from ':modules/reports/services/send-report-email.service';
import { TranslateReviewTextService } from ':modules/reports/services/translate-review-text.service';
import { DailyReviewReportsMapper } from ':modules/reports/use-cases/review-reports/daily-reports/daily-reports.mapper';
import { DailyReviewsReport } from ':modules/reports/use-cases/review-reports/daily-reports/reviews-report-daily.entity';
import { DailyReviewsReportsData } from ':modules/reports/use-cases/review-reports/review-reports.types';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { UsersRepository } from ':modules/users/users.repository';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';
import { ReviewsSemanticAnalysisService } from ':services/semantic-analysis/reviews/reviews.semantic-analysis.service';
import { ReviewSemanticAnalysis } from ':services/semantic-analysis/reviews/reviews.semantic-analysis.types';

@singleton()
export class DailyReviewsSendReportUseCase {
    private _apiKey!: string;
    private readonly reportType: ReportType = ReportType.DAILY_REVIEWS;
    private readonly SEND_REPORT_EMAILS_CHUNK_SIZE = 5;
    private readonly INTERVAL_BETWEEN_EMAILS = 5;

    constructor(
        private readonly _reportsRepository: ReportsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _dailyReviewReportsMapper: DailyReviewReportsMapper,
        private readonly _reviewsSemanticAnalysisService: ReviewsSemanticAnalysisService,
        private readonly _apiKeysRepository: ApiKeysRepository,
        private readonly _sendReportEmailService: SendReportEmailService,
        private readonly _generateReportPDFService: GenerateReportPDFService,
        private readonly _deleteReportPDFService: DeleteReportPDFService,
        private readonly _translateReviewTextService: TranslateReviewTextService,
        private readonly _usersRepository: UsersRepository
    ) {}

    async execute({ reportId, configurationId, options }: ReportSendParamsValidatorDto): Promise<void> {
        const [key, report] = await Promise.all([
            this._apiKeysRepository.findOne({
                filter: { name: 'email' },
                options: { lean: true },
            }),
            this._reportsRepository.getReportById({ reportId }),
        ]);

        assert(
            report,
            new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Report not found',
                metadata: { reportId },
            })
        );
        assert(key, '[DailyReviewsSendReportUseCase] API key not found');
        this._apiKey = key.apiKey;

        const configuration = report?.configurations?.find((config) => config.id === configurationId);

        if (isNil(report) || isNil(configuration)) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Report not found',
                metadata: {
                    reportId,
                },
            });
        }

        // For debug, send to custom emails
        if (options?.sendTo) {
            configuration.recipients = options.sendTo;
        }

        const reportData = await this._getDailyReportData(report, configuration);
        assert(reportData, '[DailyReviewsSendReportUseCase] Report data not found');
        const sendReports = await this._sendReports(reportData, options?.shouldPreventEmailSending);

        await processPromisesByChunks(sendReports, this.SEND_REPORT_EMAILS_CHUNK_SIZE, async (_) => {
            await waitFor(this.INTERVAL_BETWEEN_EMAILS * TimeInMilliseconds.SECOND);
        });

        await this._deleteReportPdf(reportData);
    }

    private async _getDailyReportData(report: Report, config: Report['configurations'][0]): Promise<DailyReviewsReport | null> {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            logger.info(`${report.getLogGroup()} Get report data`, metaData);

            const dailyReviewsReportData = await this._getReportData(report, config);

            assert(report.user, '[DailyReviewsSendReportUseCase] User not found');
            const emailProps = await this._dailyReviewReportsMapper.mapToEmailProps(
                report.user,
                dailyReviewsReportData,
                config.restaurants
            );

            const dailyReviewsReport = new DailyReviewsReport(emailProps, report, config);

            const pdfFileUrl = await this._generateReportPDFService.execute({
                report,
                configuration: config,
                pdfFolderName: dailyReviewsReport.getPdfFolderName(),
                pdfFileName: dailyReviewsReport.getPdfFileName(),
                html: render(DailyReviewsReports(emailProps)),
            });
            if (pdfFileUrl) {
                dailyReviewsReport.setPdfFileUrl(pdfFileUrl);
            }

            return dailyReviewsReport;
        } catch (err) {
            logger.error('[Daily reviews report] Get report data', {
                err,
                ...metaData,
            });
            return null;
        }
    }

    private async _getReportData(report: Report, config: Report['configurations'][0]): Promise<DailyReviewsReportsData> {
        const metaData = report.getMetaDataFromConfig(config);

        const dailyReviewsReport: DailyReviewsReportsData = await this._reviewsRepository.getReviewsForReports(
            config.restaurants,
            this.reportType
        );

        assert(report.user, '[DailyReviewsSendReportUseCase] User not found');
        // Translating reviews
        if (dailyReviewsReport.data.length) {
            for (const restaurantData of dailyReviewsReport.data) {
                if (restaurantData.reviews.length) {
                    for (const review of restaurantData.reviews) {
                        if (review.text && review.lang !== report.user.defaultLanguage) {
                            try {
                                const translatedReview = await this._translateReviewTextService.execute(review, {
                                    _id: report.user.id.toString(),
                                    defaultLanguage: report.user.defaultLanguage,
                                });
                                if (translatedReview) {
                                    review.text = translatedReview.text;
                                }
                            } catch (error: any) {
                                logger.error('[Daily reviews report] Cannot translate review text', {
                                    reviewId: review._id,
                                    userId: report.user.id,
                                    error: error.stack,
                                });
                            }
                        }
                    }
                }
            }
        }

        // Add AI analysis
        dailyReviewsReport.aiSemanticGlobalAnalysis = await this._getAIAdvice(dailyReviewsReport.data, metaData);

        // TODO uncomment if AI analysis is needed for each restaurant
        // const dailyReviewsReportWithAI = await Promise.all(
        //     dailyReviewsReport.data.map(async (restaurantData) => {
        //         const aiSemanticAnalysis = await this._getAIAdvice([restaurantData], metaData);
        //         return { ...restaurantData, aiSemanticAnalysis };
        //     })
        // );
        // dailyReviewsReport.data = dailyReviewsReportWithAI;

        return dailyReviewsReport;
    }

    private async _getAIAdvice(restaurantsData: DailyReviewsReportsData['data'], metaData: ReportMetaData): Promise<string | undefined> {
        try {
            const restaurantsPayloadData = await Promise.all(
                restaurantsData.map(async (restaurantData) => {
                    const reviewsSemanticAnalysis = await this._getReviewsSemanticAnalysis(
                        restaurantData.restaurantId.toString(),
                        restaurantData.reviews
                    );
                    return {
                        restaurantName: restaurantData.name,
                        reviewsSemanticAnalysis,
                    };
                })
            );

            const { semanticAnalysisResult: aiSemanticAnalysis } = await this._reviewsSemanticAnalysisService.getSemanticAnalysisOverview({
                collection: AiInteractionRelatedEntityCollection.REPORTS,
                collectionId: metaData.reportId,
                language: metaData.user?.defaultLanguage ? LANGUAGES[metaData.user.defaultLanguage] : LANGUAGES[APP_DEFAULT_LANGUAGE],
                restaurantsData: restaurantsPayloadData,
            });

            if (!aiSemanticAnalysis || aiSemanticAnalysis === '') {
                logger.warn('[Daily reviews report] Semantic analysis from AI returned empty value', metaData);
                return undefined;
            }

            return aiSemanticAnalysis;
        } catch (err) {
            logger.error('[Daily reviews report] Semantic analysis from AI', {
                err,
                ...metaData,
            });

            return undefined;
        }
    }

    private async _getReviewsSemanticAnalysis(
        restaurantId: string,
        reviews: IReviewWithSemanticAnalysisAndTranslations[]
    ): Promise<ReviewSemanticAnalysis[]> {
        const isSemanticAnalysisFeatureEnabled = await isFeatureAvailableForRestaurant({
            featureName: 'release-new-semantic-analysis',
            restaurantId,
        });

        if (isSemanticAnalysisFeatureEnabled) {
            return reviews.flatMap((review) =>
                review.semanticAnalysisSegments.map(({ category, sentiment, segment }) => ({
                    category,
                    sentiment,
                    segment,
                }))
            );
        }
        return reviews.flatMap((review) =>
            review.semanticAnalysis.segmentAnalyses.map(({ tag, sentiment, originalSegment }) => ({
                category: tag,
                sentiment,
                segment: originalSegment,
            }))
        );
    }

    private async _sendReports(
        dailyReviewsReport: DailyReviewsReport,
        shouldPreventEmailSending?: boolean
    ): Promise<(() => Promise<void>)[]> {
        const metaData = dailyReviewsReport.getMetaData();
        const logGroup = dailyReviewsReport.getLogGroup();

        const validRecipients = await this._usersRepository.filterNonVerifiedKnownEmails(dailyReviewsReport.getRecipients());

        logger.info(`${logGroup} Preparing to send reports`, {
            ...metaData,
            recipientCount: dailyReviewsReport.getRecipients().length,
            validRecipientCount: validRecipients.length,
        });

        const emailsPromises: (() => Promise<void>)[] = [];
        for (const recipient of validRecipients) {
            emailsPromises.push(() => this._sendReport(dailyReviewsReport, recipient, shouldPreventEmailSending));
        }

        return emailsPromises;
    }

    private async _sendReport(dailyReviewsReport: DailyReviewsReport, recipient: string, shouldPreventEmailSending?: boolean) {
        const metaData = dailyReviewsReport.getMetaData();
        const logGroup = dailyReviewsReport.getLogGroup();

        try {
            logger.info(`${logGroup} Preparing to send report`, {
                recipient,
                ...metaData,
            });

            const unsubscribeLink = await dailyReviewsReport.buildUnsubscribeLink(recipient);
            const trackingUrl = dailyReviewsReport.buildTrackingUrl({ email: recipient, apiKey: this._apiKey });

            const html = render(
                DailyReviewsReports({
                    ...dailyReviewsReport.getEmailProps(),
                    unsubscribeLink: unsubscribeLink,
                    trackingUrl,
                })
            );

            await this._sendReportEmailService.execute({
                report: dailyReviewsReport.getReport(),
                recipient,
                configuration: dailyReviewsReport.getConfiguration(),
                reportEmailSubject: dailyReviewsReport.getReportEmailSubject(),
                language: dailyReviewsReport.getEmailProps().locale,
                pdfFileUrl: dailyReviewsReport.getPdfFileUrl(),
                html,
                shouldPreventEmailSending,
            });
        } catch (err) {
            logger.error(`${logGroup} Failed to send report`, {
                err,
                recipient,
                ...metaData,
            });
        }
    }

    async _deleteReportPdf(reportData: DailyReviewsReport): Promise<void> {
        const pdfFileUrl = reportData.getPdfFileUrl();
        if (isNil(pdfFileUrl)) {
            logger.warn(`${reportData.getLogGroup()} PDF file not found`, reportData.getMetaData());
            return;
        }

        await this._deleteReportPDFService.execute({
            report: reportData.getReport(),
            configuration: reportData.getConfiguration(),
            pdfFolderName: reportData.getPdfFolderName(),
            pdfFileName: reportData.getPdfFileName(),
            pdfFileUrl,
        });
    }
}

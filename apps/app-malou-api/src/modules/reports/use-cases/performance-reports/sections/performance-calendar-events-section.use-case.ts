import { groupBy, uniq } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import {
    CalendarEventPerformanceReportProps,
    IncomingEventsPerformanceReportProps,
    incomingEventsPerformanceReportValidator,
} from '@malou-io/package-dto';
import { APP_DEFAULT_LANGUAGE, APP_DEFAULT_LOCALE, emojiToPngUrl, isNotNil } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { GetHolidaysAndLocationEventsForRestaurantsUseCase } from ':modules/calendar-events/use-cases/get-holidays-and-location-for-restaurants/get-holidays-and-location-for-restaurants.use-case';
import { Report } from ':modules/reports/report.entity';

@singleton()
export class GetPerformanceCalendarEventsSectionUseCase {
    private readonly MAX_INCOMING_EVENTS_COUNT = 8;

    constructor(private readonly _getHolidaysAndLocationEventsForRestaurantsUseCase: GetHolidaysAndLocationEventsForRestaurantsUseCase) {}

    async execute({
        report,
        config,
        periods,
    }: {
        report: Report;
        config: Report['configurations'][0];
        periods: {
            startDate: Date;
            endDate: Date;
        };
    }): Promise<IncomingEventsPerformanceReportProps | undefined> {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            const { restaurants } = config;

            const calendarEvents = await this._getHolidaysAndLocationEventsForRestaurantsUseCase.execute({
                restaurantIds: restaurants.map(({ _id }) => _id.toString()),
                startDate: periods.startDate.toISOString(),
                endDate: periods.endDate.toISOString(),
            });

            if (calendarEvents.length === 0) {
                return undefined;
            }

            // Get unique events and format them
            const eventsGroupedById = groupBy(calendarEvents, 'calendarEvent.id');
            const locale = metaData.user?.defaultLanguage ?? APP_DEFAULT_LOCALE;

            const eventsWithStartDate: (CalendarEventPerformanceReportProps & { restaurantIds: string[] })[] = Object.keys(
                eventsGroupedById
            )
                .map((calendarEventId) => {
                    const object = eventsGroupedById[calendarEventId][0];
                    const restaurantIds = eventsGroupedById[calendarEventId].map(({ restaurantId }) => restaurantId.toString());

                    return {
                        event: {
                            ...object.calendarEvent,
                            name: object.calendarEvent.name
                                ? (object.calendarEvent.name[locale] ?? object.calendarEvent.name[APP_DEFAULT_LANGUAGE] ?? undefined)
                                : undefined,
                        },
                        restaurantIds: uniq(restaurantIds),
                    };
                })
                .map(({ event, restaurantIds }) => {
                    if (!event.date || !event.name) {
                        return null;
                    }

                    const concernedRestaurantNames = config.restaurants
                        .filter(({ _id }) => restaurantIds.includes(_id.toString()))
                        .map(({ name }) => name);

                    return {
                        metadata: {
                            date: DateTime.fromObject(event.date).toJSDate(),
                            name: event.name,
                        },
                        isHoliday: event.isBankHoliday ?? false,
                        concernedRestaurants: concernedRestaurantNames,
                        ...(event.emoji && {
                            icon: emojiToPngUrl(event.emoji),
                        }),
                        restaurantIds,
                    };
                })
                .filter(isNotNil);

            const events = eventsWithStartDate.sort((a, b) => a.metadata.date.getTime() - b.metadata.date.getTime());

            const eventsCount = events.length;
            const firstRestaurantConcernedByEventsId = events[0]?.restaurantIds[0];
            const bankHolidayEvents = events.filter((event) => event.isHoliday);
            const bankHolidayEventDates = bankHolidayEvents.map((event) =>
                DateTime.fromJSDate(event.metadata.date).toLocaleString({ month: 'long', day: '2-digit', locale })
            );
            const firstRestaurantConcernedByHolidaysId = bankHolidayEvents[0]?.restaurantIds[0];
            const shortenedEventsList = events.slice(0, this.MAX_INCOMING_EVENTS_COUNT);

            const returnData: IncomingEventsPerformanceReportProps = {
                eventsCount,
                events: shortenedEventsList,
                ...(shortenedEventsList.length < eventsCount && {
                    moreEventsCta: {
                        // eslint-disable-next-line max-len
                        link: `${process.env.BASE_URL}/restaurants/${firstRestaurantConcernedByEventsId}/seo/informations?from_email=${report.type}&clicked_on=see_more_events_cta`,
                    },
                }),
                bankHolidayEventDates,
                cta: {
                    // eslint-disable-next-line max-len
                    link: `${process.env.BASE_URL}/restaurants/${firstRestaurantConcernedByHolidaysId}/seo/informations?from_email=${report.type}&clicked_on=update_holiday_informations_cta`,
                },
            };

            incomingEventsPerformanceReportValidator.parse(returnData);
            return returnData;
        } catch (err) {
            logger.error(`${report.getLogGroup()} Calendar events section`, {
                err,
                ...metaData,
            });

            return undefined;
        }
    }
}

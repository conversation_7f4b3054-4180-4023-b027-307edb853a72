import { render } from '@react-email/render';
import assert from 'assert';
import { isNil } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import {
    BoosterStatsPerformanceReportProps,
    GmbPerformanceReportValidatorProps,
    KeywordsPerformanceReportProps,
    MonthlyPerformanceReportProps,
    ReviewsStatsPerformanceReportProps,
    SocialMediaPerformanceReportProps,
} from '@malou-io/package-dto';
import { MonthlyPerformanceReport } from '@malou-io/package-emails';
import {
    MalouErrorCode,
    mapApplicationLanguageToLocale,
    processPromisesByChunks,
    TimeInMilliseconds,
    waitFor,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { ApiKeysRepository } from ':modules/api-keys/api-keys.repository';
import { Report } from ':modules/reports/report.entity';
import { ReportSendParamsValidatorDto } from ':modules/reports/reports.dto';
import ReportsRepository from ':modules/reports/reports.repository';
import { DeleteReportPDFService } from ':modules/reports/services/delete-report-pdf.service';
import { GenerateReportPDFService } from ':modules/reports/services/generate-report-pdf.service';
import { SendReportEmailService } from ':modules/reports/services/send-report-email.service';
import { PerformanceMonthlyReport } from ':modules/reports/use-cases/performance-reports/performance-report-monthly.entity';
import { IPerformancePeriods } from ':modules/reports/use-cases/performance-reports/performance-reports.interface';
import { GetPerformanceBoostersSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-boosters-section.use-case';
import { GetGmbPostsMonthlyPerformanceRecommendationsUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-gmb-posts-recommendation-monthly.use-case';
import { GetPerformanceGmbSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-gmb-section.use-case';
import { GetPerformanceKeywordsSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-keywords-section.use-case';
import { GetPerformanceReviewsSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-reviews-section.use-case';
import { GetSocialMediaPostsMonthlyPerformanceRecommendationsUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-social-media-posts-recommendation-monthly.use-case';
import { GetPerformanceSocialMediaSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-social-media-section.use-case';
import { UsersRepository } from ':modules/users/users.repository';

@singleton()
export class MonthlyPerformanceSendReportUseCase {
    private _apiKey!: string;
    private readonly SEND_REPORT_EMAILS_CHUNK_SIZE = 5;
    private readonly INTERVAL_BETWEEN_EMAILS = 5;

    constructor(
        private readonly _reportsRepository: ReportsRepository,
        private readonly _apiKeysRepository: ApiKeysRepository,
        private readonly _getPerformanceGmbSectionUseCase: GetPerformanceGmbSectionUseCase,
        private readonly _getPerformanceKeywordsSectionUseCase: GetPerformanceKeywordsSectionUseCase,
        private readonly _getPerformanceReviewsSectionUseCase: GetPerformanceReviewsSectionUseCase,
        private readonly _getPerformanceBoostersSectionUseCase: GetPerformanceBoostersSectionUseCase,
        private readonly _getPerformanceSocialNetworksSectionUseCase: GetPerformanceSocialMediaSectionUseCase,
        private readonly _getGmbPostsMonthlyPerformanceRecommendationsUseCase: GetGmbPostsMonthlyPerformanceRecommendationsUseCase,
        // eslint-disable-next-line max-len
        private readonly _getSocialMediaPostsMonthlyPerformanceRecommendationsUseCase: GetSocialMediaPostsMonthlyPerformanceRecommendationsUseCase,
        private readonly _sendReportEmailService: SendReportEmailService,
        private readonly _generateReportPDFService: GenerateReportPDFService,
        private readonly _deleteReportPDFService: DeleteReportPDFService,
        private readonly _usersRepository: UsersRepository
    ) {}

    async execute({ reportId, configurationId, options }: ReportSendParamsValidatorDto): Promise<void> {
        const [key, report] = await Promise.all([
            this._apiKeysRepository.findOne({
                filter: { name: 'email' },
                options: { lean: true },
            }),
            this._reportsRepository.getReportById({ reportId }),
        ]);

        assert(
            report,
            new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Report not found',
                metadata: { reportId },
            })
        );
        assert(key, '[MonthlyPerformanceSendReportUseCase] API key not found');
        this._apiKey = key.apiKey;

        const configuration = report?.configurations?.find((config) => config.id === configurationId);

        if (isNil(report) || isNil(configuration)) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Report not found',
                metadata: {
                    reportId,
                },
            });
        }

        // For debug, send to custom emails
        if (options?.sendTo) {
            configuration.recipients = options.sendTo;
        }

        const reportData = await this._getPerformanceMonthlyReport(report, configuration);
        assert(reportData, '[MonthlyPerformanceSendReportUseCase] Report data not found');

        const sendReports = await this._sendReports(reportData, options?.shouldPreventEmailSending);

        await processPromisesByChunks(sendReports, this.SEND_REPORT_EMAILS_CHUNK_SIZE, async (_) => {
            await waitFor(this.INTERVAL_BETWEEN_EMAILS * TimeInMilliseconds.SECOND);
        });

        await this._deleteReportPdf(reportData);
    }

    private async _getPerformanceMonthlyReport(
        report: Report,
        config: Report['configurations'][0]
    ): Promise<PerformanceMonthlyReport | null> {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            logger.info(`${report.getLogGroup()} Get report data`, metaData);

            const data = await this._getReportData({
                report,
                config,
            });

            const emailProps: MonthlyPerformanceReportProps = {
                concernedRestaurants: config.restaurants,
                gmb: data.gmbInsights,
                keywords: data.keywords,
                reviews: data.reviews,
                socialMedia: data.socialMedia,
                booster: data.booster,
                locale: mapApplicationLanguageToLocale(report.user?.defaultLanguage),
                period: data.period,
            };

            const performanceMonthlyReport = new PerformanceMonthlyReport(emailProps, report, config);

            const pdfFileUrl = await this._generateReportPDFService.execute({
                report,
                configuration: config,
                pdfFolderName: performanceMonthlyReport.getPdfFolderName(),
                pdfFileName: performanceMonthlyReport.getPdfFileName(),
                html: render(MonthlyPerformanceReport(emailProps)),
            });
            if (pdfFileUrl) {
                performanceMonthlyReport.setPdfFileUrl(pdfFileUrl);
            }

            return performanceMonthlyReport;
        } catch (err) {
            logger.error(`${report.getLogGroup()} Get report data`, {
                err,
                ...metaData,
            });
            return null;
        }
    }

    private async _sendReports(
        performanceMonthlyReport: PerformanceMonthlyReport,
        shouldPreventEmailSending?: boolean
    ): Promise<(() => Promise<void>)[]> {
        const metaData = performanceMonthlyReport.getMetaData();
        const logGroup = performanceMonthlyReport.getLogGroup();

        const validRecipients = await this._usersRepository.filterNonVerifiedKnownEmails(performanceMonthlyReport.getRecipients());

        logger.info(`${logGroup} Preparing to send reports`, {
            ...metaData,
            recipientCount: performanceMonthlyReport.getRecipients().length,
            validRecipientCount: validRecipients.length,
        });

        const emailsPromises: (() => Promise<void>)[] = [];
        for (const recipient of validRecipients) {
            emailsPromises.push(() => this._sendReport(performanceMonthlyReport, recipient, shouldPreventEmailSending));
        }

        return emailsPromises;
    }

    private async _sendReport(
        performanceMonthlyReport: PerformanceMonthlyReport,
        recipient: string,
        shouldPreventEmailSending?: boolean
    ): Promise<void> {
        const logGroup = performanceMonthlyReport.getLogGroup();
        const metaData = {
            recipient,
            ...performanceMonthlyReport.getMetaData(),
        };

        try {
            logger.info(`${logGroup} Preparing to send report`, metaData);

            const unsubscribeLink = await performanceMonthlyReport.buildUnsubscribeLink(recipient);
            const trackingUrl = performanceMonthlyReport.buildTrackingUrl({ email: recipient, apiKey: this._apiKey });

            const html = render(
                MonthlyPerformanceReport({
                    ...performanceMonthlyReport.getEmailProps(),
                    unsubscribeLink,
                    trackingUrl,
                })
            );

            await this._sendReportEmailService.execute({
                report: performanceMonthlyReport.getReport(),
                recipient,
                configuration: performanceMonthlyReport.getConfiguration(),
                reportEmailSubject: performanceMonthlyReport.getReportEmailSubject(),
                language: performanceMonthlyReport.getEmailProps().locale,
                pdfFileUrl: performanceMonthlyReport.getPdfFileUrl(),
                html,
                shouldPreventEmailSending,
            });
        } catch (err) {
            logger.error(`${logGroup} Failed to send report`, {
                err,
                ...metaData,
            });
        }
    }

    async _deleteReportPdf(reportData: PerformanceMonthlyReport): Promise<void> {
        const pdfFileUrl = reportData.getPdfFileUrl();

        if (isNil(pdfFileUrl)) {
            logger.warn(`${reportData.getLogGroup()} PDF file not found`, reportData.getMetaData());
            return;
        }

        await this._deleteReportPDFService.execute({
            report: reportData.getReport(),
            configuration: reportData.getConfiguration(),
            pdfFolderName: reportData.getPdfFolderName(),
            pdfFileName: reportData.getPdfFileName(),
            pdfFileUrl,
        });
    }

    private async _getReportData({ report, config }: { report: Report; config: Report['configurations'][0] }): Promise<{
        gmbInsights: GmbPerformanceReportValidatorProps | undefined;
        keywords: KeywordsPerformanceReportProps | undefined;
        reviews: ReviewsStatsPerformanceReportProps | undefined;
        booster: BoosterStatsPerformanceReportProps | undefined;
        socialMedia?: SocialMediaPerformanceReportProps;
        period: IPerformancePeriods['current'];
    }> {
        const periods: IPerformancePeriods = {
            current: {
                startDate: DateTime.now().minus({ months: 1 }).startOf('month').toJSDate(),
                endDate: DateTime.now().minus({ months: 1 }).endOf('month').toJSDate(),
            },
            previous: {
                startDate: DateTime.now().minus({ months: 2 }).startOf('month').toJSDate(),
                endDate: DateTime.now().minus({ months: 2 }).endOf('month').toJSDate(),
            },
        };

        const [
            mappedGmbEmailSection,
            gmbPostsMonthlyRecommendation,
            mappedKeywordsEmailSection,
            reviewInsightsEmailSection,
            boosterInsightsEmailSection,
            socialMedia,
            socialMediaPostsRecommendation,
        ] = await Promise.all([
            this._getPerformanceGmbSectionUseCase.execute({ report, config, periods }),
            this._getGmbPostsMonthlyPerformanceRecommendationsUseCase.execute({ report, config, periods }),
            this._getPerformanceKeywordsSectionUseCase.execute({ report, config, periods }),
            this._getPerformanceReviewsSectionUseCase.execute({ report, config, periods }),
            this._getPerformanceBoostersSectionUseCase.execute({ report, config, periods }),
            this._getPerformanceSocialNetworksSectionUseCase.execute({ report, config, periods }),
            this._getSocialMediaPostsMonthlyPerformanceRecommendationsUseCase.execute({ report, config, periods }),
        ]);

        return {
            gmbInsights: mappedGmbEmailSection
                ? {
                      ...mappedGmbEmailSection,
                      postsMonthlyRecommendation: gmbPostsMonthlyRecommendation,
                  }
                : undefined,
            keywords: mappedKeywordsEmailSection,
            reviews: reviewInsightsEmailSection,
            booster: boosterInsightsEmailSection,
            ...(socialMedia && {
                socialMedia: {
                    instagram: {
                        ...socialMedia.instagram,
                        postsMonthlyRecommendation: socialMediaPostsRecommendation,
                    },
                },
            }),
            period: periods.current,
        };
    }
}

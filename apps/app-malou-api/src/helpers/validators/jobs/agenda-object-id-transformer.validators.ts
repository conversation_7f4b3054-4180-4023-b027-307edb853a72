import { Types } from 'mongoose';
import { z } from 'zod';

import { ObjectId } from '@malou-io/package-models';

/**
 * Agenda uses an old version of mongodb driver
 */
export const agendaObjectIdTransformerValidator = z.custom<Types.ObjectId>((value: unknown) =>
    typeof value === 'object' && value !== null && value.toString().match(/^[0-9a-fA-F]{24}$/) ? new ObjectId(value.toString()) : false
);

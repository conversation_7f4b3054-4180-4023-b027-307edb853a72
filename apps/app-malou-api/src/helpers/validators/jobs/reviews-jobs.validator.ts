import { z } from 'zod';

import { objectIdValidator } from '@malou-io/package-dto';

import { agendaObjectIdTransformerValidator } from ':helpers/validators/jobs/agenda-object-id-transformer.validators';

export const autoReplyToReviewValidator = z.object({
    reviewId: objectIdValidator,
    replyText: z.string(),
    templateId: objectIdValidator.or(agendaObjectIdTransformerValidator).optional(),
    interactionId: objectIdValidator.or(agendaObjectIdTransformerValidator).optional(),
    replyLang: z.string().nullish(),
});

export const retryReplyToReviewValidator = z.object({
    commentId: objectIdValidator.or(agendaObjectIdTransformerValidator),
    args: z.object({
        user: z.record(z.string(), z.any()),
        reviewId: objectIdValidator,
        comment: z.record(z.string(), z.any()),
        restaurantId: objectIdValidator,
        headerConfig: z.record(z.string(), z.any()),
    }),
});

import 'reflect-metadata';

import ':env';

import { chunk } from 'lodash';
import { autoInjectable, container } from 'tsyringe';

import { DbId, IRestaurant, toDbId } from '@malou-io/package-models';
import { SemanticAnalysisFetchStatus } from '@malou-io/package-utils';

import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { ReviewSemanticAnalysisProducer } from ':modules/segment-analyses/queues/review-semantic-analysis/review-semantic-analysis.producer';
import ':plugins/db';

@autoInjectable()
class FetchSemanticAnalysis {
    private readonly _REVIEW_CHUNK_SIZE = 100;

    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _privateReviewsRepository: PrivateReviewsRepository,
        private readonly _reviewSemanticAnalysisProducer: ReviewSemanticAnalysisProducer
    ) {
        this._reviewSemanticAnalysisProducer.initialize();
    }

    async execute() {
        const restaurantId = '6268039d8de2974d89ebd445';
        const restaurant = await this._restaurantsRepository.findOne({
            filter: { _id: toDbId(restaurantId) },
            projection: { _id: 1, name: 1 },
        });
        if (!restaurant) {
            console.log(`Restaurant with id ${restaurantId} not found`);
            return;
        }
        console.log(`Will update semantic analysis for restaurant ${restaurant.name}`);

        const filter = {
            restaurantId: restaurant._id,
            text: { $ne: null },
            socialCreatedAt: {
                $gte: new Date('2025-01-01T00:00:00.000Z'),
            },
        };
        const options: { lean: boolean; sort: { socialCreatedAt: 1 | -1 } } = {
            lean: true,
            sort: { socialCreatedAt: 1 },
        };

        await this._fetchSemanticAnalysisForReviews(restaurant, filter, options);
        await this._fetchSemanticAnalysisForPrivateReviews(restaurant, filter, options);
    }

    private async _fetchSemanticAnalysisForReviews(restaurant: Pick<IRestaurant, '_id' | 'name'>, filter, options): Promise<void> {
        const reviews = await this._reviewsRepository.find({
            filter,
            projection: { _id: 1 },
            options,
        });
        const reviewChunks = chunk(reviews, this._REVIEW_CHUNK_SIZE);
        for (const reviewChunk of reviewChunks) {
            try {
                await Promise.all(
                    reviewChunk.map((review) => this._fetchSemanticAnalysisForSingleReview(review._id, restaurant._id, false))
                );
                console.log(`Finished ${reviewChunk.length} reviews...`);
            } catch (err) {
                console.log(`Error for restaurant ${restaurant._id}`);
            }
        }
    }

    private async _fetchSemanticAnalysisForPrivateReviews(restaurant: Pick<IRestaurant, '_id' | 'name'>, filter, options): Promise<void> {
        const privateReviews = await this._privateReviewsRepository.find({
            filter,
            projection: { _id: 1 },
            options,
        });
        const privateReviewChunks = chunk(privateReviews, this._REVIEW_CHUNK_SIZE);
        for (const privateReviewChunk of privateReviewChunks) {
            try {
                await Promise.all(
                    privateReviewChunk.map((privateReview) =>
                        this._fetchSemanticAnalysisForSingleReview(privateReview._id, restaurant._id, true)
                    )
                );
                console.log(`Finished ${privateReviewChunk.length} private reviews...`);
            } catch (err) {
                console.log(`Error for restaurant ${restaurant._id}`);
            }
        }
    }

    private async _fetchSemanticAnalysisForSingleReview(reviewId: DbId, restaurantId: DbId, isPrivateReview: boolean): Promise<void> {
        await this._reviewSemanticAnalysisProducer.execute({
            reviewId: reviewId.toString(),
            restaurantId: restaurantId.toString(),
            isPrivateReview,
        });
        const repository: any = isPrivateReview ? this._privateReviewsRepository : this._reviewsRepository;
        await repository.findOneAndUpdate({
            filter: { _id: reviewId },
            update: { semanticAnalysisFetchStatus: SemanticAnalysisFetchStatus.PENDING },
        });
    }
}
const task = container.resolve(FetchSemanticAnalysis);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });

import 'reflect-metadata';

import ':env';

import { chunk } from 'lodash';
import { autoInjectable, container } from 'tsyringe';

import { DbId, IRestaurant, toDbIds } from '@malou-io/package-models';
import { SemanticAnalysisFetchStatus } from '@malou-io/package-utils';

import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { ReviewSemanticAnalysisProducer } from ':modules/segment-analyses/queues/review-semantic-analysis/review-semantic-analysis.producer';
import ':plugins/db';

@autoInjectable()
class FetchSemanticAnalysis {
    private readonly _MAX_REVIEWS_LIMIT_PER_RESTAURANT = Infinity;
    private readonly _REVIEW_CHUNK_SIZE = 100;

    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _privateReviewsRepository: PrivateReviewsRepository,
        private readonly _reviewSemanticAnalysisProducer: ReviewSemanticAnalysisProducer
    ) {
        this._reviewSemanticAnalysisProducer.initialize();
    }

    // Task can make up to 2000 restaurants x 200 reviews = 400k requests to the lambda, we don't mind if it is slow (8000 chunks of 50 reviews)
    async execute() {
        const restaurantIds = []; // fill if necessary

        const restaurants = await this._restaurantsRepository.find({
            filter: {
                active: true,
                ...(restaurantIds.length ? { _id: { $in: toDbIds(restaurantIds) } } : {}),
            },
            projection: { _id: 1, name: 1 },
        });

        console.log(`Will update semantic analysis for ${restaurants.length} restaurants`);
        let index = 0;
        for (const restaurant of restaurants) {
            console.log('Starting restaurant', index, ' - ', restaurant.name);
            const filter = {
                restaurantId: restaurant._id,
                text: { $ne: null },
                socialCreatedAt: {
                    $gte: new Date('2025-01-01T00:00:00.000Z'),
                },
            };
            const options: { lean: boolean; sort: { socialCreatedAt: 1 | -1 }; limit: number } = {
                lean: true,
                sort: { socialCreatedAt: 1 },
                limit: this._MAX_REVIEWS_LIMIT_PER_RESTAURANT,
            };

            await this._fetchSemanticAnalysisForReviews(restaurant, filter, options);
            await this._fetchSemanticAnalysisForPrivateReviews(restaurant, filter, options);

            index++;
        }
    }

    private async _fetchSemanticAnalysisForReviews(restaurant: Pick<IRestaurant, '_id' | 'name'>, filter, options): Promise<void> {
        const reviews = await this._reviewsRepository.find({
            filter,
            projection: { _id: 1 },
            options,
        });
        const reviewChunks = chunk(reviews, this._REVIEW_CHUNK_SIZE);
        for (const reviewChunk of reviewChunks) {
            try {
                await Promise.all(
                    reviewChunk.map((review) => this._fetchSemanticAnalysisForSingleReview(review._id, restaurant._id, false))
                );
                console.log(`Finished ${reviewChunk.length} reviews...`);
            } catch (err) {
                console.log(`Error for restaurant ${restaurant._id}`);
            }
        }
    }

    private async _fetchSemanticAnalysisForPrivateReviews(restaurant: Pick<IRestaurant, '_id' | 'name'>, filter, options): Promise<void> {
        const privateReviews = await this._privateReviewsRepository.find({
            filter,
            projection: { _id: 1 },
            options,
        });
        const privateReviewChunks = chunk(privateReviews, this._REVIEW_CHUNK_SIZE);
        for (const privateReviewChunk of privateReviewChunks) {
            try {
                await Promise.all(
                    privateReviewChunk.map((privateReview) =>
                        this._fetchSemanticAnalysisForSingleReview(privateReview._id, restaurant._id, true)
                    )
                );
                console.log(`Finished ${privateReviewChunk.length} private reviews...`);
            } catch (err) {
                console.log(`Error for restaurant ${restaurant._id}`);
            }
        }
    }

    private async _fetchSemanticAnalysisForSingleReview(reviewId: DbId, restaurantId: DbId, isPrivateReview: boolean): Promise<void> {
        await this._reviewSemanticAnalysisProducer.execute({
            reviewId: reviewId.toString(),
            restaurantId: restaurantId.toString(),
            isPrivateReview,
        });
        const repository: any = isPrivateReview ? this._privateReviewsRepository : this._reviewsRepository;
        await repository.findOneAndUpdate({
            filter: { _id: reviewId },
            update: { semanticAnalysisFetchStatus: SemanticAnalysisFetchStatus.PENDING },
        });
    }
}
const task = container.resolve(FetchSemanticAnalysis);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });

import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { GenerateTailwindConfigurationService } from ':modules/store-locator/services/generate-tailwind-configuration/generate-tailwind-configuration.service';
import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
class CreateOrganizationConfigurationTask {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _generateTailwindConfigurationService: GenerateTailwindConfigurationService
    ) {}

    async execute(): Promise<void> {
        // const config = await this._getTailwindConfiguration('67cf1ef531d778287af0d2ef');
        // await this._handleBioBurgerConfiguration();
        await this._updateBolkiriConfiguration();
    }

    private async _handleBioBurgerConfiguration(): Promise<void> {
        await this._storeLocatorOrganizationConfigRepository.updateOne({
            filter: {
                organizationId: toDbId('66695c8079a84d7da2a8e4cd'),
            },
            update: {
                plugins: {
                    googleAnalytics: {
                        trackingId: 'G-BB80WJF683',
                    },
                },
                isLive: true,
                styles: {
                    fonts: [
                        {
                            class: 'primary',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/66695c8079a84d7da2a8e4cd/fonts/primary.woff',
                        },
                        {
                            class: 'primary-bold',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/66695c8079a84d7da2a8e4cd/fonts/primary-bold.woff',
                            weight: '900',
                        },
                    ],
                    colors: [
                        {
                            class: 'primary',
                            value: '#19422c',
                        },
                        {
                            class: 'secondary',
                            value: '#eb7049',
                        },
                        {
                            class: 'tertiary',
                            value: '#fcf6ec',
                        },
                        {
                            class: 'fourth',
                            value: '#c4a696',
                        },
                    ],
                    pages: {
                        store: {
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]: [
                                'bg-tertiary',
                                'text-primary',
                                'mt-[116px]',
                                'xl:h-[calc(100vh-116px)]',
                            ],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]: ['font-primary-bold', 'text-secondary'],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]: ['fill-secondary'],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER]: ['bg-tertiary'],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]: [
                                'border-primary',
                                'text-primary',
                                'hover:bg-primary',
                                'hover:text-white',
                                'rounded-full',
                            ],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2]: [
                                'border-primary',
                                'bg-primary',
                                'text-white',
                                'hover:text-primary',
                                'rounded-full',
                            ],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_ICON]: ['fill-primary'],
                            [StoreLocatorRestaurantPageElementIds.GALLERY_WRAPPER]: ['bg-fourth', 'text-primary'],
                            [StoreLocatorRestaurantPageElementIds.GALLERY_TITLE]: ['font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER]: ['bg-primary'],
                            [StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE]: ['text-secondary', 'font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.REVIEWS_CTA]: [
                                'bg-tertiary',
                                'text-primary',
                                'hover:bg-primary',
                                'hover:text-tertiary',
                                'hover:border-tertiary',
                                'rounded-full',
                            ],
                            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER]: ['bg-fourth'],
                            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE]: ['text-primary', 'font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA]: [
                                'bg-primary',
                                'hover:bg-tertiary',
                                'hover:text-primary',
                                'border-primary',
                                'text-white',
                                'rounded-full',
                            ],
                            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_WRAPPER]: ['bg-secondary'],
                            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_TITLE]: ['text-tertiary', 'font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE]: ['text-tertiary'],
                            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE_NAME]: ['font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN]: ['bg-tertiary', 'text-primary'],
                            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN]: ['font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN]: ['bg-secondary', 'text-tertiary'],
                            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN]: ['font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_TITLE]: ['text-primary', 'font-primary-bold'],
                        },
                    },
                },
            },
        });
    }

    private async _createBolkiriConfiguration(): Promise<void> {
        await this._storeLocatorOrganizationConfigRepository.create({
            data: {
                organizationId: toDbId('67cf1ef531d778287af0d2ef'),
                cloudfrontDistributionId: 'ED691UB5YSI29',
                baseUrl: 'https://restaurants.bolkiri.fr',
                styles: {
                    fonts: [
                        {
                            class: 'primary',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/67cf1ef531d778287af0d2ef/fonts/Satoshi-Medium.woff',
                        },
                        {
                            class: 'primary-bold',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/67cf1ef531d778287af0d2ef/fonts/titanone.woff2',
                            weight: '900',
                        },
                        {
                            class: 'secondary',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/67cf1ef531d778287af0d2ef/fonts/dosis.woff2',
                        },
                        {
                            class: 'tertiary',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/67cf1ef531d778287af0d2ef/fonts/titanone.woff2',
                        },
                    ],
                    colors: [
                        {
                            class: 'primary',
                            value: '#fbc513',
                        },
                        {
                            class: 'secondary',
                            value: '#E94F43',
                        },
                        {
                            class: 'tertiary',
                            value: '#656161',
                        },
                        {
                            class: 'fourth',
                            value: '#D3D3D3',
                        },
                    ],
                    pages: {
                        store: {},
                    },
                },
            },
        });
    }

    private async _updateBolkiriConfiguration(): Promise<void> {
        await this._storeLocatorOrganizationConfigRepository.updateOne({
            filter: { organizationId: toDbId('67cf1ef531d778287af0d2ef') },
            update: {
                isLive: false,
                'styles.pages': {
                    store: {
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]: [
                            'bg-white',
                            'text-tertiary',
                            'mt-[80px]',
                            'xl:h-[calc(100vh-80px)]',
                        ],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]: ['font-tertiary', 'text-primary'],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]: ['fill-secondary'],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER]: ['bg-white'],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]: [
                            'border-tertiary',
                            'text-tertiary',
                            'hover:bg-tertiary',
                            'hover:text-white',
                            'rounded-sm',
                        ],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2]: [
                            'border-primary',
                            'bg-primary',
                            'text-white',
                            'hover:text-primary',
                            'rounded-sm',
                        ],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_ICON]: ['fill-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.GALLERY_WRAPPER]: ['bg-fourth', 'text-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.GALLERY_TITLE]: ['font-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.GALLERY_PICTURE]: ['rounded-lg'],
                        [StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER]: ['bg-secondary'],
                        [StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE]: ['text-primary', 'font-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.REVIEWS_CTA]: [
                            'bg-primary',
                            'text-white',
                            'border-primary',
                            'hover:bg-secondary',
                            'hover:text-primary',
                            'rounded-lg',
                        ],
                        [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER]: ['bg-fourth'],
                        [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE]: ['text-tertiary', 'font-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA]: [
                            'bg-primary',
                            'border-primary',
                            'text-white',
                            'hover:bg-white',
                            'hover:border-primary',
                            'hover:text-primary',
                            'rounded-lg',
                        ],
                        [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_WRAPPER]: ['bg-white'],
                        [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_TITLE]: ['text-primary', 'font-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE]: ['text-black'],
                        [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE_NAME]: ['font-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN]: ['bg-white', 'text-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN]: ['font-tertiary', 'text-secondary'],
                        [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN]: ['bg-white', 'text-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN]: ['font-tertiary', 'text-primary'],
                        [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_TITLE]: ['text-primary', 'font-primary-bold'],
                    },
                },
            },
        });
    }

    private async _getTailwindConfiguration(organizationId: string) {
        const storeLocatorOrganizationConfig = await this._storeLocatorOrganizationConfigRepository.findOne({
            filter: { organizationId },
            options: { lean: true },
        });

        if (!storeLocatorOrganizationConfig) {
            throw new Error(`No configuration found for organization ${organizationId}`);
        }

        // Assuming GenerateTailwindConfigurationService is implemented and available
        return this._generateTailwindConfigurationService.execute({ storeLocatorOrganizationConfig });
    }
}

const task = container.resolve(CreateOrganizationConfigurationTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });

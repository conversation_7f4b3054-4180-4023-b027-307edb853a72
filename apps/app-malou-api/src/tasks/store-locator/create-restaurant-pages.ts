import 'reflect-metadata';

import ':env';

import ':di';
import { from<PERSON>uff<PERSON> } from 'file-type';
import { promises as fs } from 'fs';
import lodash from 'lodash';
import assert from 'node:assert';
import path from 'path';
import sharp from 'sharp';
import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { cleanUrl, isNotNil, StoreLocatorLanguage } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { AiMediaDescriptionService } from ':microservices/ai-media-description';
import { GenerateMediaDescriptionImageType } from ':modules/ai/interfaces/ai.interfaces';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import StoreLocatorRestaurantPageRepository from ':modules/store-locator/store-locator-restaurant-page.repository';
import { AwsS3 } from ':plugins/cloud-storage/s3';
import { GoogleSheetsService } from ':services/google-sheets/google-sheets.service';

@singleton()
class CreateRestaurantsConfigTask {
    private readonly _GSHEET_DATA_ID = '1qzedWigeqs1CEs7YkHq0Hoj7pJEKJ--KLlvUDMeaI6w';
    private readonly _ORGANIZATION_ID = '67cf1ef531d778287af0d2ef';
    private readonly _KEYWORDS = 'Restaurant asiatique, Cuisine vietnamienne, Street food';
    private readonly _CUISINE_TYPE = 'Vietnamienne, street food, asiatique';
    private readonly _BASE_URL = 'https://restaurants.bolkiri.fr/';

    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _googleSheetsService: GoogleSheetsService,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _cloudStorageService: AwsS3,
        private readonly _aiMediaDescriptionService: AiMediaDescriptionService
    ) {}

    async execute(): Promise<void> {
        const organizationSheet = await this._googleSheetsService.loadGoogleSheet(this._GSHEET_DATA_ID);
        const organizationWorkSheet = organizationSheet.sheetsByTitle['Correction (à éditer)'];

        await organizationWorkSheet.loadCells('A1:AD20'); // loads range of cells into local cache - DOES NOT RETURN THE CELLS

        let column = 8;
        let organizationSheetColumn = 1;
        let restaurantName = organizationWorkSheet.getCell(0, organizationSheetColumn + 1).value.toString() as string;

        while (restaurantName) {
            column++;
            organizationSheetColumn++;

            try {
                // Get restaurant ID
                restaurantName = organizationWorkSheet.getCell(0, organizationSheetColumn).value as string;
                if (!restaurantName) {
                    break;
                }
                const restaurant = await this._restaurantsRepository.findOne({
                    filter: { name: { $regex: RegExp(restaurantName, 'i') } },
                    projection: { _id: 1 },
                    options: { lean: true },
                });
                assert(restaurant);

                const restaurantPage = await this._storeLocatorRestaurantPageRepository.findOne({
                    filter: { restaurantId: restaurant._id },
                    options: { lean: true },
                });
                if (restaurantPage) {
                    logger.info('Restaurant page already exists', { restaurantId: restaurant._id.toString() });
                    continue;
                }

                logger.info('data', { column, organizationSheetColumn, restaurantName, restaurantId: restaurant._id.toString() });
                const imageUrls = await this._uploadImagesAndGetUrl({ restaurantId: restaurant._id.toString(), restaurantName });
                const descriptions = this._getDescriptions(
                    organizationWorkSheet.getCell(11, organizationSheetColumn).value.toString() as string,
                    imageUrls.descriptions
                );
                const ctas = this._getCtas(organizationWorkSheet.getCell(14, organizationSheetColumn).value.toString() as string);
                const fullUrl = organizationWorkSheet.getCell(5, organizationSheetColumn).value.toString() as string;
                const relativePath = fullUrl.replace(this._BASE_URL, '');
                const lang = StoreLocatorLanguage.FR;

                const imagesAlt = this._printImageAlts(imageUrls);

                const cta = {
                    text: 'Commander',
                    url: ctas.find((cta) => cta.url?.includes('zelty'))?.url || ctas[0].url,
                };

                const restaurantConfig: any = {
                    // TODO: Define a proper type for this
                    restaurantId: restaurant._id,
                    relativePath,
                    fullUrl,
                    lang,
                    organizationId: toDbId(this._ORGANIZATION_ID),
                    blocks: {
                        head: {
                            title: organizationWorkSheet.getCell(3, organizationSheetColumn).value.toString() as string,
                            description: organizationWorkSheet.getCell(4, organizationSheetColumn).value.toString() as string,
                            twitterDescription: organizationWorkSheet.getCell(6, organizationSheetColumn).value.toString() as string,
                            keywords: this._KEYWORDS,
                            schemaOrgCuisineType: this._CUISINE_TYPE,
                            facebookImageUrl: imageUrls.head[0].url,
                            twitterImageUrl: imageUrls.head[1].url,
                            snippetImageUrl: imageUrls.head[2].url,
                        },
                        information: {
                            title: organizationWorkSheet.getCell(2, organizationSheetColumn).value.toString() as string,
                            image: {
                                url: imageUrls.information[0].url,
                                description: imageUrls.information[0].description,
                            },
                            cta,
                        },
                        gallery: {
                            title: organizationWorkSheet.getCell(7, organizationSheetColumn).value.toString() as string,
                            subtitle: organizationWorkSheet.getCell(8, organizationSheetColumn).value.toString() as string,
                            images: imageUrls.gallery,
                        },
                        reviews: {
                            title: organizationWorkSheet.getCell(9, organizationSheetColumn).value.toString() as string,
                            cta,
                        },
                        callToActions: {
                            title: organizationWorkSheet.getCell(13, organizationSheetColumn).value.toString() as string,
                            ctas,
                        },
                        socialNetworks: {
                            title: organizationWorkSheet.getCell(10, organizationSheetColumn).value.toString() as string,
                            socialNetworks: {},
                        },
                        descriptions,
                    },
                };

                await this._storeLocatorRestaurantPageRepository.create({ data: restaurantConfig });
            } catch (err) {
                logger.error('Error creating restaurant config', { err, restaurantName, organizationSheetColumn });
            }
        }
    }

    private _getDescriptions(descs: string, images: { url: string; description: string }[]) {
        const descriptions = descs.replaceAll('\n', '');
        const descriptionsArray = descriptions.split('############');

        return {
            items: descriptionsArray
                .map((desc, index) => {
                    const [title, blocsRaw] = desc.trim().split('########');
                    const blocks = blocsRaw
                        .trim()
                        .split('######')
                        .map((b) => {
                            const [titleBloc, text] = b.split('####');
                            return {
                                title: titleBloc.trim(),
                                text: text.trim(),
                            };
                        });

                    return {
                        title: title.trim(),
                        image: images[index],
                        blocks,
                    };
                })
                .filter(isNotNil),
        };
    }

    private _getCtas(rawCtas: string) {
        if (!rawCtas) {
            return [];
        }

        const ctas = rawCtas.split('\n\n');

        return ctas.map((cta) => {
            const [text, url] = cta.trim().split('\n');
            logger.info('CTAS', { text, url });

            return {
                text: text.trim(),
                url: cleanUrl(url.trim()),
            };
        });
    }

    // Convert HEIC to PNG in all folders and subfolders
    // find . -type f -iname "*.heic" -exec sh -c 'sips -s format png "$1" --out "${1%.*}.png"' _ {} \; -exec rm {} \;
    private async _uploadImagesAndGetUrl({ restaurantId, restaurantName }: { restaurantId: string; restaurantName: string }): Promise<{
        head: { url: string }[];
        information: { url: string; description: string }[];
        gallery: { url: string; description: string }[];
        descriptions: { url: string; description: string }[];
    }> {
        // Clear S3 bucket
        const baseS3Url = `store-locator/organization/${this._ORGANIZATION_ID}/restaurants/${restaurantId}`;
        try {
            await this._cloudStorageService.emptyDirectory(baseS3Url);
        } catch (err) {
            logger.warn('[STORE LOCATOR]Failed to empty publications pictures folder', { err });
        }

        // Read images from gallery folder
        const folderPath = path.join(__dirname, 'images', restaurantName);
        const images = await fs.readdir(folderPath);

        if (images.length === 0) {
            throw new Error('folder not ready');
        }

        const imageUrls = {
            head: [],
            information: [],
            gallery: [],
            descriptions: [],
        };

        // Group them by letters
        const lettersToSection = {
            A: 'information',
            B: 'gallery',
            C: 'descriptions',
            D: 'head',
        };
        const imagesPerSection = lodash.groupBy(images, (image) => image[0]);
        await Promise.all(
            Object.keys(imagesPerSection).map((letter) =>
                Promise.all(
                    imagesPerSection[letter].map(async (image) => {
                        try {
                            const filePath = path.join(folderPath, image);
                            const imageIndex = image.match(/^([A-Z]+\d+)/i)?.[1]?.replace(letter, '');
                            const folder = lettersToSection[letter];
                            const imageType = {
                                information: GenerateMediaDescriptionImageType.ALT_TEXT_INFORMATION_BLOCK,
                                gallery: GenerateMediaDescriptionImageType.ALT_TEXT_GALLERY_BLOCK,
                                descriptions: GenerateMediaDescriptionImageType.ALT_TEXT_DESCRIPTIONS_BLOCK,
                            }[folder];

                            // Use original image as-is
                            const buffer = await fs.readFile(filePath);
                            const imageRemoteKey = `${baseS3Url}/${folder}/photo${imageIndex}`;

                            // Resize to max 2048px for AI analysis
                            const [resizedBuffer, bufferForAi] = await Promise.all([
                                this._resizeImage({ buffer, maxSize: 3000 }),
                                this._resizeImage({ buffer, maxSize: 2048, toJpeg: true }),
                            ]);

                            // Upload to S3
                            const uploadedPicture = await this._uploadPicture({
                                buffer: resizedBuffer,
                                bufferForAi,
                                s3Key: imageRemoteKey,
                            });

                            if (!uploadedPicture) {
                                logger.error('Failed to upload picture', { imageRemoteKey });
                                return undefined;
                            }
                            const { uploadedUrl, uploadedUrlForAi } = uploadedPicture;

                            // Get description alt
                            let description;
                            if (imageType) {
                                const data = await this._aiMediaDescriptionService.generateMediaDescription({
                                    imageLink: uploadedUrlForAi,
                                    imageType,
                                    language: StoreLocatorLanguage.FR,
                                    keywords: this._KEYWORDS.split(', '),
                                    restaurantName,
                                });
                                description = data.aiResponse.description;
                            }

                            imageUrls[folder].push({ url: uploadedUrl, index: Number(imageIndex), ...(description && { description }) });
                            console.log(`✅ Uploaded: ${imageRemoteKey}`);
                        } catch (error) {
                            console.error(`❌ Failed to process file`, error);
                        }
                    })
                )
            )
        );

        // Sort by index
        Object.keys(imageUrls).forEach((key) => {
            imageUrls[key] = imageUrls[key]
                .sort((a, b) => a.index - b.index)
                .map((image) => ({
                    url: image.url,
                    ...(image.description && { description: image.description }),
                }));
        });

        return imageUrls;
    }

    private _printImageAlts(imageUrls) {
        const keyToLetter = {
            information: 'A',
            gallery: 'B',
            descriptions: 'C',
        };
        return Object.keys(imageUrls)
            .filter((key) => keyToLetter[key])
            .map((key) => {
                return imageUrls[key].map((image, index) => `${keyToLetter[key]}${index + 1}: ${image.description}`).join('\n');
            })
            .join('\n');
    }

    private async _resizeImage({ buffer, maxSize, toJpeg }: { buffer: Buffer; maxSize: number; toJpeg?: boolean }): Promise<Buffer> {
        // Get metadata to check dimensions
        const metadata = await sharp(buffer).metadata();
        const isPortrait = (metadata.height || 0) > (metadata.width || 0);

        // Resize based on the longest side
        const transform = sharp(buffer)
            .rotate() // Prevents EXIF-based auto-rotation
            .resize({
                width: isPortrait ? undefined : maxSize,
                height: isPortrait ? maxSize : undefined,
                fit: 'inside', // maintain aspect ratio
                withoutEnlargement: true, // prevent upsizing if smaller
            });

        // Convert to JPEG format
        if (toJpeg) {
            transform.jpeg({
                quality: 80, // reasonable balance between quality and size
                chromaSubsampling: '4:2:0', // standard for better compression
            });
        }

        return await transform.toBuffer();
    }

    private async _uploadPicture({
        buffer,
        bufferForAi,
        s3Key,
    }: {
        buffer: Buffer;
        bufferForAi: Buffer;
        s3Key: string;
    }): Promise<{ uploadedUrl: string; uploadedUrlForAi: string } | undefined> {
        try {
            const [fileTypeResult, fileTypeResultForAi] = await Promise.all([fromBuffer(buffer), fromBuffer(bufferForAi)]);
            if (!fileTypeResult || !fileTypeResultForAi) {
                logger.error('[STORE_LOCATOR] [Images] Failed to get file type when uploading picture', {
                    s3Key,
                });
                return undefined;
            }

            const { ext, mime } = fileTypeResult;
            const { ext: extForAi, mime: mimeForAi } = fileTypeResultForAi;
            const [uploadedUrl, uploadedUrlForAi] = await Promise.all([
                this._cloudStorageService.uploadBuffer({
                    buffer: buffer,
                    fileKey: `${s3Key}.${ext}`,
                    mimeType: mime,
                }),
                this._cloudStorageService.uploadBuffer({
                    buffer: bufferForAi,
                    fileKey: `${s3Key}-for-ai.${extForAi}`,
                    mimeType: mimeForAi,
                }),
            ]);

            logger.info('[STORE_LOCATOR] [Images] Uploaded image to S3', {
                s3Key: `${s3Key}.${ext}`,
                s3KeyForAi: `${s3Key}-for-ai.${extForAi}`,
            });

            return { uploadedUrl, uploadedUrlForAi };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Images] Failed to upload picture', {
                s3Key,
                err,
            });

            return undefined;
        }
    }
}

const task = container.resolve(CreateRestaurantsConfigTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });

import { z } from 'astro/zod';
import dotenv from 'dotenv';
import { loadEnv } from 'vite';

const envValidator = z.object({
    ORGANIZATION_ID: z.string(),
    ORGANIZATION_BASE_URL: z.string(),
    API_KEY: z.string(),
    API_BASE_URL: z.string(),
    ENVIRONMENT: z.enum(['production', 'test']).default('test'),
});

const validateEnv = (env: Record<string, unknown>) => {
    const parsedEnv = envValidator.safeParse(env);

    if (!parsedEnv.success) {
        console.error(
            'Invalid environment variables:',
            parsedEnv.error.format(),
        );
        throw new Error('Invalid environment variables');
    }

    return parsedEnv.data;
};

let config: z.infer<typeof envValidator>;
if (typeof import.meta !== 'undefined' && import.meta.env) {
    // Vite/Astro context
    config = validateEnv(
        loadEnv(process.env.NODE_ENV || 'local', process.cwd(), ''),
    );
} else {
    // Node.js context - use dotenv
    dotenv.config({
        path: `.env.${process.env.NODE_ENV || 'local'}`,
    });
    config = validateEnv(process.env);
}

export { config };

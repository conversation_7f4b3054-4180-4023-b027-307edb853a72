import { z } from 'astro/zod';
import { loadEnv } from 'vite';

const envValidator = z.object({
    ORGANIZATION_ID: z.string(),
    ORGANIZATION_BASE_URL: z.string(),
    API_KEY: z.string(),
    API_BASE_URL: z.string(),
    ENVIRONMENT: z.enum(['production', 'test']).default('test'),
});

const validateEnv = (env: Record<string, unknown>) => {
    const parsedEnv = envValidator.safeParse(env);

    if (!parsedEnv.success) {
        console.error(
            'Invalid environment variables:',
            parsedEnv.error.format(),
        );
        throw new Error('Invalid environment variables');
    }

    return parsedEnv.data;
};

export const config = validateEnv(
    loadEnv(process.env.NODE_ENV || 'local', process.cwd(), ''),
);

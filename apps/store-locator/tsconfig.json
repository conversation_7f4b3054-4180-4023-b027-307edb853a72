{
    // For possible configs: https://github.com/withastro/astro/tree/main/packages/astro/tsconfigs
    "extends": "astro/tsconfigs/strictest",
    "compilerOptions": {
        "baseUrl": ".",
        "paths": {
            ":assets/*": ["./src/assets/*"],
            ":components/*": ["./src/components/*"],
            ":interfaces/*": ["./src/interfaces/*"],
            ":layouts/*": ["./src/layouts/*"],
            ":styles/*": ["./src/styles/*"],
            ":utils/*": ["./src/utils/*"],
            ":i18n/*": ["./src/i18n/*"],
            ":config": ["./config.ts"]
        }
    },
    "references": [
        {
            "path": "../../packages/malou-dto"
        }
    ],
    "include": [".astro/types.d.ts", "**/*"],
    "exclude": ["dist"]
}

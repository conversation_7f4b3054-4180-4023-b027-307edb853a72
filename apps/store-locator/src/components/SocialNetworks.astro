---
import Bookmark from ':assets/icons/bookmark.svg';
import Comment from ':assets/icons/comment.svg';
import Heart from ':assets/icons/heart.svg';
import Play from ':assets/icons/play.svg';
import Share from ':assets/icons/share.svg';
import { initTranslationFunction } from ':i18n/index';
import type { StorePage } from ':interfaces/pages.interfaces';
import ':styles/global.css';
import { getStyles } from ':utils/get-element-styles';
import { Picture } from 'astro:assets';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    socialNetworksBlock: NonNullable<StorePage['socialNetworksBlock']>;
    styles: StorePage['styles'];
}

const { socialNetworksBlock, styles } = Astro.props as Props;
const getElementStyles = getStyles({ styles });
const t = await initTranslationFunction();
---

<div class={`${getElementStyles({ elementId: 'social-networks-wrapper' })}`}>
    <div class="mx-auto max-w-[1600px] px-0 py-12 sm:px-6">
        <h2
            class={`${getElementStyles({ elementId: 'social-networks-title' })} px-2 pb-8 text-center text-2xl font-extrabold sm:px-0 sm:text-4xl`}
        >
            {socialNetworksBlock.title.toUpperCase()}
        </h2>

        <div class="flex items-center justify-center gap-4">
            <a
                href={socialNetworksBlock.socialNetworks.instagram.socialAccount
                    .url}
                aria-label={t(
                    'socialNetworks.instagram.profile.link.aria-label',
                )}
                target="_blank"
                class={`${getElementStyles({ elementId: 'social-networks-profile' })} flex items-center gap-4`}
            >
                <Picture
                    src={socialNetworksBlock.socialNetworks.instagram
                        .socialAccount.logoUrl}
                    formats={['webp']}
                    fallbackFormat="jpg"
                    class="rounded-full object-cover object-center"
                    alt={t(
                        'socialNetworks.instagram.profile.profilePicture.label',
                        {
                            userName:
                                socialNetworksBlock.socialNetworks.instagram
                                    .socialAccount.name,
                        },
                    )}
                    width={80}
                    height={80}
                    densities={[1, 2, 3]}
                />
                <div class="flex flex-col">
                    <span
                        class={`${getElementStyles({ elementId: 'social-networks-profile-name' })} font-bold text-xl`}
                    >
                        {
                            socialNetworksBlock.socialNetworks.instagram
                                .socialAccount.name
                        }</span
                    >
                    <span class="text-sm"
                        >{
                            t('socialNetworks.instagram.profile.stats', {
                                followersCount:
                                    socialNetworksBlock.socialNetworks.instagram
                                        .socialAccount.followersCount,
                                publicationsCount:
                                    socialNetworksBlock.socialNetworks.instagram
                                        .socialAccount.publicationsCount,
                            })
                        }
                    </span>
                </div>
            </a>
        </div>

        <div
            class="no-scrollbar mx-auto flex flex-nowrap gap-3 overflow-x-auto py-12 xl:justify-center"
        >
            {
                socialNetworksBlock.socialNetworks.instagram.publications.map(
                    (publication, index) => (
                        <a
                            href={publication.url}
                            aria-label={`Publication Instagram n°${index + 1}`}
                            target="_blank"
                            class="flex items-stretch gap-4"
                        >
                            <div class="w-[250px] flex-shrink-0 overflow-hidden rounded-md border border-gray-200 bg-white shadow-xl">
                                <div class="relative h-[360px] w-[250px]">
                                    <Picture
                                        src={publication.imageUrl}
                                        formats={['webp']}
                                        fallbackFormat="jpg"
                                        class="h-full w-full object-cover object-center"
                                        alt={publication.imageDescription}
                                        width={250}
                                        height={360}
                                        densities={[1, 2, 3]}
                                    />
                                    {publication.isFirstMediaVideo && (
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <Play />
                                        </div>
                                    )}
                                </div>
                                <div class="flex flex-col gap-3 p-4">
                                    <div class="relative flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <Heart />
                                            <Comment />
                                            <Share />
                                        </div>
                                        {publication.mediaCount > 1 && (
                                            <div class="absolute inset-0 m-auto flex items-center justify-center gap-1">
                                                {Array.from({
                                                    length: publication.mediaCount,
                                                }).map((_, index) => (
                                                    <div
                                                        class={`${index === 0 ? 'h-[8px] w-[8px] bg-[#2C86FD]' : 'h-[6px] w-[6px] bg-[#0a2540] opacity-50'} rounded-full`}
                                                    />
                                                ))}
                                            </div>
                                        )}

                                        <div>
                                            <Bookmark />
                                        </div>
                                    </div>

                                    {publication.likesCount !== '0' && (
                                        <p class="text-xs font-extrabold">
                                            {t(
                                                'socialNetworks.instagram.publication.likes',
                                                {
                                                    likesCount:
                                                        publication.likesCount,
                                                },
                                            )}
                                        </p>
                                    )}
                                    <p class="helvetica line-clamp-8 text-xs text-gray-700">
                                        {publication.caption}
                                    </p>
                                </div>
                            </div>
                        </a>
                    ),
                )
            }
        </div>
    </div>
</div>

declare namespace NodeJS {
    interface ProcessEnv {
        ORGANIZATION_BASE_URL: string;
    }
}

// https://docs.astro.build/en/guides/environment-variables/#intellisense-for-typescript
interface ImportMetaEnv {
    // Env variables only accessible during build time
    readonly ORGANIZATION_ID: string;
    readonly API_KEY: string;
    readonly API_BASE_URL: string;
    readonly ENVIRONMENT: 'production' | 'test';

    // Env variables accessible to the client should start with PUBLIC_
    // https://docs.astro.build/en/guides/environment-variables/#vites-built-in-support
}

interface ImportMeta {
    readonly env: ImportMetaEnv;
}

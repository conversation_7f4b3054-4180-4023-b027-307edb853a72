import { config } from ':config';
import axios, { type AxiosResponse } from 'axios';
import assert from 'node:assert';
import { promises as fs } from 'node:fs';
import path from 'node:path';

import type { GetStoreLocatorOrganizationConfigurationDto } from '@malou-io/package-dto';

async function generateTailwindFiles() {
    console.log('Fetching tailwind files from API...');
    const response: AxiosResponse<{
        data: GetStoreLocatorOrganizationConfigurationDto;
    }> = await axios.get(
        `${config.API_BASE_URL}/store-locator/${config.ORGANIZATION_ID}/configuration?api_key=${config.API_KEY}`,
    );
    const tailwindConfig = response?.data?.data?.tailwindConfig;
    const tailwindClassesMap = response?.data?.data?.tailwindClassesMap;

    assert(tailwindConfig);
    assert(tailwindClassesMap);

    await Promise.all([
        fs.writeFile(path.join('src', 'styles', 'global.css'), tailwindConfig),
        fs.writeFile(
            path.join('src', 'styles', 'classes.mapping.ts'),
            tailwindClassesMap,
        ),
    ]);
}

// Call the function
generateTailwindFiles()
    .then(() => {
        console.log('tailwind config files generated successfully.');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error during tailwind config file generation', error);
        process.exit(1);
    });

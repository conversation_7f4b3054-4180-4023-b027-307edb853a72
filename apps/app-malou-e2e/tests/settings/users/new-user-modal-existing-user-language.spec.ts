import { expect, it } from 'baseTest';
import { createTestUser, deleteTestUser } from 'tests/settings/users/utils';

import { ApplicationLanguage } from '@malou-io/package-utils';

it.describe('New User Modal - Existing User Language', () => {
    const testUserEmail = `test-user-${Date.now()}@example.com`;
    let testUserId: string;

    it('should preserve existing user language and disable language dropdown', async ({ page }) => {
        // Step 1: Create a random user in the database with Italian language
        testUserId = await createTestUser({
            email: testUserEmail,
            language: ApplicationLanguage.IT,
        });

        // Step 2: Login as admin and navigate to Le Nino Traiteur settings/roles
        const restaurantId = process.env.RESTAURANT_ID_LE_NINO;
        // await page.goto(`/restaurants/${restaurantId}/dashboard`, { waitUntil: 'commit' });
        // await login({ page, email: E2E_ADMIN_USER.email, password: E2E_ADMIN_USER.password! });

        // Step 3: Go to the settings/roles page
        await page.goto(`/restaurants/${restaurantId}/settings/roles`, { waitUntil: 'commit' });
        await page.waitForSelector('mat-table', { state: 'visible' });

        // Step 4: Click on the "Add User" button to open the modal
        // The button is in the header template, look for the button with the openAddUser() click handler
        const addUserButton = page
            .locator('button')
            .filter({ hasText: /add.*user/i })
            .first();
        await addUserButton.click();

        // Wait for the modal to appear
        const modal = page.locator('app-new-user-modal');
        await expect(modal).toBeVisible();

        // Step 5: Click on the email input and type the full email of the created user
        const emailSelect = modal.locator('app-select[formControlName="user"]');

        // Click on the input field within the select component
        const emailInput = emailSelect.locator('input');
        await emailInput.click();
        await emailInput.fill(testUserEmail);

        // Press Enter to trigger the itemBuilder
        await emailInput.press('Enter');

        // Step 6: Wait for the itemBuilder to process the email and populate the user data
        await page.waitForTimeout(2000); // Give time for the async itemBuilder to complete

        // Step 7: Check that in the language dropdown, Italian is selected
        const languageDropdown = modal.locator('app-select-languages[formControlName="language"]');

        // Check if the language dropdown shows Italian flag or text
        const italianFlag = languageDropdown.locator('img[alt="it"], img[src*="it"]');
        await expect(italianFlag).toBeVisible();

        // Or check for Italian text
        const italianText = languageDropdown.getByText('Italian', { exact: false });
        await expect(italianText).toBeVisible();

        // Step 8: Check that the language dropdown is disabled
        // The disabled state should be reflected in the component's disabled input
        const isLanguageDropdownDisabled = await languageDropdown.evaluate((el) => {
            // Check if the component has disabled class or if the underlying input is disabled
            return el.classList.contains('opacity-50') || el.querySelector('input')?.disabled || el.hasAttribute('disabled');
        });

        expect(isLanguageDropdownDisabled).toBe(true);

        // Step 9: Try to click on the language dropdown to verify it doesn't open
        await languageDropdown.click();

        // Wait a moment to see if any dropdown options appear
        await page.waitForTimeout(500);

        // Check that no dropdown options are visible (because it's disabled)
        const dropdownOptions = page.locator('.mat-option, .mat-autocomplete-panel');
        await expect(dropdownOptions).not.toBeVisible();

        // Close the modal by clicking the close button
        const closeButton = modal.locator('mat-icon[svgIcon*="cross"], .close');
        await closeButton.click();

        // Verify modal is closed
        await expect(modal).not.toBeVisible();
    });

    // Cleanup: Remove the test user from the database
    it.afterAll(async () => {
        if (testUserEmail) {
            await deleteTestUser(testUserEmail);
        }
    });
});

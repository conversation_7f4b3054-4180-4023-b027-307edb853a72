import { CalendarEventCountry } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const calendarEventJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'CalendarEvent',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        date: {
            type: 'object',
            additionalProperties: false,
            properties: {
                day: { type: 'number' },
                month: { type: 'number' },
                year: { type: 'number' },
            },
            required: ['day', 'month', 'year'],
        },
        emoji: {
            type: 'string',
        },
        country: {
            enum: Object.values(CalendarEventCountry),
        },
        name: {
            $ref: '#/definitions/Names',
        },
        byDefault: {
            type: 'boolean',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        example: {
            $ref: '#/definitions/Example',
        },
        ideas: {
            $ref: '#/definitions/Example',
        },
        isBankHoliday: {
            type: 'boolean',
        },
        shouldSuggestSpecialHourUpdate: {
            type: 'boolean',
        },
        shouldSuggestToPost: {
            type: 'object',
            additionalProperties: false,
            properties: {
                active: {
                    type: 'boolean',
                },
                concernedRestaurantCategories: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                },
            },
            required: ['active', 'concernedRestaurantCategories'],
        },
    },
    required: [
        '_id',
        'date',
        'byDefault',
        'name',
        'country',
        'shouldSuggestSpecialHourUpdate',
        'shouldSuggestToPost',
        'createdAt',
        'updatedAt',
    ],
    definitions: {
        Example: {
            type: 'object',
            additionalProperties: false,
            properties: {
                fr: {
                    type: 'string',
                },
                en: {
                    type: 'string',
                },
            },
            required: [],
            title: 'Example',
        },
        Names: {
            type: 'object',
            additionalProperties: false,
            properties: {
                fr: {
                    type: 'string',
                    nullable: true,
                },
                en: {
                    type: 'string',
                    nullable: true,
                },
                es: {
                    type: 'string',
                    nullable: true,
                },
                it: {
                    type: 'string',
                    nullable: true,
                },
            },
            required: [],
            title: 'Names',
        },
    },
} as const satisfies JSONSchemaExtraProps;

import { cleanUrl, isValidUrl, StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const storeLocatorOrganizationConfigJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        organizationId: {
            type: 'string',
            format: 'objectId',
        },
        cloudfrontDistributionId: {
            type: 'string',
        },
        baseUrl: {
            type: 'string',
            format: 'uri',
            validate: {
                validator: (v) => isValidUrl(v),
                message: (props) => `Url should be valid, value: ${props.value}`,
            },
            set: cleanUrl,
        },
        isLive: {
            type: 'boolean',
            default: false,
            description: 'Indicates if the store locator is still in development mode',
        },
        styles: {
            $ref: '#/definitions/Styles',
        },
        plugins: {
            $ref: '#/definitions/Plugins',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    required: ['_id', 'createdAt', 'updatedAt', 'organizationId', 'cloudfrontDistributionId', 'baseUrl', 'isLive', 'styles'],
    definitions: {
        Styles: {
            type: 'object',
            additionalProperties: false,
            properties: {
                fonts: {
                    $ref: '#/definitions/Fonts',
                },
                colors: {
                    $ref: '#/definitions/Colors',
                },
                pages: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        store: {
                            $ref: '#/definitions/StorePageStyles',
                        },
                    },
                    required: ['store'],
                },
            },
            required: ['fonts', 'colors', 'pages'],
            title: 'Styles',
        },
        Fonts: {
            type: 'array',
            items: {
                type: 'object',
                additionalProperties: false,
                properties: {
                    class: {
                        type: 'string',
                        enum: [
                            'primary',
                            'primary-bold',
                            'secondary',
                            'secondary-bold',
                            'tertiary',
                            'tertiary-bold',
                            'fourth',
                            'fourth-bold',
                            'fifth',
                            'fifth-bold',
                            'sixth',
                            'sixth-bold',
                        ],
                    },
                    src: {
                        type: 'string',
                        format: 'uri',
                        validate: {
                            validator: (v) => isValidUrl(v),
                            message: (props) => `Url should be valid, value: ${props.value}`,
                        },
                    },
                    weight: {
                        type: 'string',
                        enum: ['400', '700', '900'],
                    },
                    style: {
                        type: 'string',
                        enum: ['normal', 'italic'],
                    },
                },
                required: ['class', 'src'],
            },
            title: 'Fonts',
        },
        Colors: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    class: {
                        type: 'string',
                        enum: ['primary', 'secondary', 'tertiary', 'fourth', 'fifth', 'sixth'],
                    },
                    value: {
                        type: 'string',
                        validate: {
                            validator: (v) => /^#?([0-9A-F]{3,4}|[0-9A-F]{6}|[0-9A-F]{8})$/i.test(v),
                            message: (props) => `Color should be in hexa format, value: ${props.value}`,
                        },
                    },
                },
                required: ['class', 'value'],
            },
            title: 'Colors',
        },
        StorePageStyles: {
            type: 'object',
            additionalProperties: true,
            properties: {},
            validate: {
                validator: (v) => {
                    const allowedElementIds = Object.values(StoreLocatorRestaurantPageElementIds);
                    return Object.keys(v).every((key) => allowedElementIds.includes(key as StoreLocatorRestaurantPageElementIds));
                },
                message: (props) => `Styles should only contain valid HTML element IDs, value: ${props.value}`,
            },
            title: 'StorePageStyles',
        },
        Plugins: {
            type: 'object',
            additionalProperties: false,
            properties: {
                googleAnalytics: {
                    $ref: '#/definitions/GoogleAnalyticsPlugin',
                },
            },
            title: 'Plugins',
        },
        GoogleAnalyticsPlugin: {
            type: 'object',
            additionalProperties: false,
            properties: {
                trackingId: {
                    type: 'string',
                },
            },
            required: ['trackingId'],
            title: 'GoogleAnalyticsPlugin',
        },
    },
    title: 'StoreLocatorOrganizationConfig',
} as const satisfies JSONSchemaExtraProps;

{
    "extends": "../../tsconfig.options.json",
    "compilerOptions": {
        "rootDir": "./src",
        "outDir": "./lib",
        "types": ["jest", "node"],
        // Set `sourceRoot` to  "/" to strip the build path prefix from
        // generated source code references. This will improve issue grouping in Sentry.
        "sourceRoot": "/",
        "noImplicitAny": false
    },
    "include": ["src"],
    "references": [
        {
            "path": "../malou-utils"
        }
    ]
}

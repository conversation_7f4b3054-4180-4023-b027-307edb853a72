import { z } from 'zod';

import { urlValidator } from '../../utils';

export const storeLocatorStorePageHeadBlockValidator = z.object({
    title: z.string(),
    description: z.string(),
    twitterDescription: z.string(),
    keywords: z.string(),
    url: urlValidator(),
    snippetImageUrl: urlValidator(),
    facebookImageUrl: urlValidator(),
    twitterImageUrl: urlValidator(),
    locale: z.string(),
    organizationName: z.string(),
    xUserName: z.string().optional(),
    googleAnalyticsId: z.string().optional(),
    microdata: z.string(),
    favIconUrl: urlValidator(),
    isLive: z.boolean(),
});

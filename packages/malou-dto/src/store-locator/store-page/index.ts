import { z } from 'zod';

import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { objectIdValidator } from '../../utils';
import { storeLocatorStorePageCallToActionsBlockValidator } from './call-to-actions-block';
import { storeLocatorStorePageDescriptionsBlockValidator } from './descriptions-block';
import { storeLocatorStorePageGalleryBlockValidator } from './gallery-block';
import { storeLocatorStorePageHeadBlockValidator } from './head-block';
import { storeLocatorStorePageInformationBlockValidator } from './information-block';
import { storeLocatorStorePageReviewsBlockValidator } from './reviews-block';
import { storeLocatorStorePageSocialNetworksBlockValidator } from './social-networks-block';

export const storeLocatorStoreValidator = z.object({
    id: objectIdValidator,
    name: z.string(),
    internalName: z.string().optional(),
    relativePath: z.string(),
    organizationName: z.string(),
    lang: z.nativeEnum(StoreLocatorLanguage),
    headBlock: storeLocatorStorePageHeadBlockValidator,
    galleryBlock: storeLocatorStorePageGalleryBlockValidator,
    reviewsBlock: storeLocatorStorePageReviewsBlockValidator.optional(),
    callToActionsBlock: storeLocatorStorePageCallToActionsBlockValidator,
    socialNetworksBlock: storeLocatorStorePageSocialNetworksBlockValidator,
    descriptionsBlock: storeLocatorStorePageDescriptionsBlockValidator,
    informationBlock: storeLocatorStorePageInformationBlockValidator,
    styles: z.record(
        z.string(), // z.nativeEnum(StoreLocatorRestaurantPageElementIds),
        z.array(z.string()) // Values are arrays of strings
    ),
});

export type GetStoreLocatorStorePageDto = z.infer<typeof storeLocatorStoreValidator>;

export {
    storeLocatorStorePageCallToActionsBlockValidator,
    storeLocatorStorePageDescriptionsBlockValidator,
    storeLocatorStorePageGalleryBlockValidator,
    storeLocatorStorePageHeadBlockValidator,
    storeLocatorStorePageInformationBlockValidator,
    storeLocatorStorePageReviewsBlockValidator,
    storeLocatorStorePageSocialNetworksBlockValidator,
};

import { z } from 'zod';

import { RestaurantCalendarEventsCountry } from '@malou-io/package-utils';

import { objectIdValidator } from '../utils/validators';

export const updateRestaurantCalendarEventsCountryParamsValidator = z.object({
    restaurantId: objectIdValidator,
});
export type UpdateRestaurantCalendarEventsCountryParamsDto = z.infer<typeof updateRestaurantCalendarEventsCountryParamsValidator>;

export const updateRestaurantCalendarEventsCountryBodyValidator = z.object({
    calendarEventsCountry: z.nativeEnum(RestaurantCalendarEventsCountry),
});
export type UpdateRestaurantCalendarEventsCountryBodyDto = z.infer<typeof updateRestaurantCalendarEventsCountryBodyValidator>;

import { z } from 'zod';

import { objectIdValidator } from '../utils';

export const createCalendarEventForRestaurantParamsValidator = z.object({
    restaurantId: objectIdValidator,
});

export type CreateCalendarEventForRestaurantParamsDto = z.infer<typeof createCalendarEventForRestaurantParamsValidator>;

export const createCalendarEventForRestaurantBodyValidator = z.object({
    date: z.object({
        day: z.number(),
        month: z.number(),
        year: z.number(),
    }),
    name: z.object({
        fr: z.string().nullish(),
        en: z.string().nullish(),
        es: z.string().nullish(),
        it: z.string().nullish(),
    }),
    emoji: z.string().optional(),
});

export type CreateCalendarEventForRestaurantBodyDto = z.infer<typeof createCalendarEventForRestaurantBodyValidator>;

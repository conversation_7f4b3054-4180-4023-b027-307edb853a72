import { z } from 'zod';

import { CalendarEventCountry } from '@malou-io/package-utils';

import { dayMonthYearValidator, objectIdValidator } from '../utils';

export const searchCalendarEventsBodyValidator = z.object({
    restaurantId: objectIdValidator,
    startDate: dayMonthYearValidator,
    endDate: dayMonthYearValidator,
    country: z.nativeEnum(CalendarEventCountry),
});

export type SearchCalendarEventsBodyDto = z.infer<typeof searchCalendarEventsBodyValidator>;

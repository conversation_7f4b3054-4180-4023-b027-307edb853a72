import { DayMonthYear } from '@malou-io/package-utils';

export interface CalendarEventNameDto {
    en?: string | null;
    fr?: string | null;
    es?: string | null;
    it?: string | null;
}

export interface CalendarEventExampleDto {
    fr?: string;
    en?: string;
}

export interface CalendarEventDto {
    id: string;
    date: DayMonthYear;
    emoji?: string;
    country: string;
    name: CalendarEventNameDto;
    byDefault: boolean;
    example?: CalendarEventExampleDto;
    ideas?: CalendarEventExampleDto;
    isBankHoliday?: boolean;
    shouldSuggestSpecialHourUpdate: boolean;
    shouldSuggestToPost: {
        active: boolean;
        concernedRestaurantCategories: string[];
    };
    createdAt: Date;
    updatedAt: Date;
}

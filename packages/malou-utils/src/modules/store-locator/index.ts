export enum StoreLocatorLanguage {
    EN = 'en',
    FR = 'fr',
}

export enum StoreLocatorRestaurantPageElementIds {
    INFORMATION_WRAPPER = 'information-wrapper',
    INFORMATION_TITLE = 'information-title',
    INFORMATION_ICONS = 'information-icons',
    INFORMATION_BANNER = 'information-banner',
    INFORMATION_BANNER_CTA_1 = 'information-banner-cta-1',
    INFORMATION_BANNER_CTA_2 = 'information-banner-cta-2',
    INFORMATION_BANNER_CTA_ICON = 'information-banner-cta-icon',
    GALLERY_WRAPPER = 'gallery-wrapper',
    GALLERY_TITLE = 'gallery-title',
    GALLERY_PICTURE = 'gallery-picture',
    REVIEWS_WRAPPER = 'reviews-wrapper',
    REVIEWS_TITLE = 'reviews-title',
    REVIEWS_CTA = 'reviews-cta',
    CALL_TO_ACTIONS_WRAPPER = 'call-to-actions-wrapper',
    CALL_TO_ACTIONS_TITLE = 'call-to-actions-title',
    CALL_TO_ACTIONS_CTA = 'call-to-actions-cta',
    SOCIAL_NETWORKS_WRAPPER = 'social-networks-wrapper',
    SOCIAL_NETWORKS_TITLE = 'social-networks-title',
    SOCIAL_NETWORKS_PROFILE = 'social-networks-profile',
    SOCIAL_NETWORKS_PROFILE_NAME = 'social-networks-profile-name',
    DESCRIPTIONS_BLOCK_EVEN = 'descriptions-block-even',
    DESCRIPTIONS_BLOCK_TITLE_EVEN = 'descriptions-block-title-even',
    DESCRIPTIONS_BLOCK_UNEVEN = 'descriptions-block-uneven',
    DESCRIPTIONS_BLOCK_TITLE_UNEVEN = 'descriptions-block-title-uneven',
    DESCRIPTIONS_TITLE = 'descriptions-title',
}
